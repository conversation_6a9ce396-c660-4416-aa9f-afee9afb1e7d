# Irrigation & Reservoir State Ingestion Flow

This note documents how irrigation-plan and reservoir automation snapshots move from MQTT packets into the database and how the "current" projection stays in sync. Use it as the authoritative reference when modifying mutations, triggers, or migrations related to these tables.

## 1. MQTT worker pipeline

1. **Decode packet**
   - `packages/mqtt-integration/src/irriganet/irrigation-plan-state-calculator.ts` and `reservoir-state-calculator.ts` turn device payloads into domain objects.
   - Calculators infer `end_time` when the device reports completion without a timestamp and mark `end_time_inferred = true`.

2. **Batch history upsert**
   - `scheduling-report-package.ts` calls `batchInsertIrrigationPlanStates`.
   - `automation-report-package.ts` calls `batchInsertReservoirStates`.
   - Both functions write directly into the TimescaleDB hypertables using `ON CONFLICT (entity, start_time) DO UPDATE` to merge fresher packets for the same cycle and to clear the inferred flag only when a definitive `end_time` arrives.

3. **No direct writes to current tables**
   - Workers never touch `current_irrigation_plan_state` or `current_reservoir_state`. This guarantees the history tables remain the single source of truth.

## 2. Database triggers

| Trigger | Fired on | Purpose |
|---------|----------|---------|
| `sync_current_irrigation_plan_state` | `AFTER INSERT OR UPDATE` on `irrigation_plan_state` | Projects the canonical cycle into `current_irrigation_plan_state`, replacing the row when a newer cycle starts, a fresher packet arrives, or a definitive end time replaces an inferred one. |
| `sync_current_reservoir_state` | `AFTER INSERT OR UPDATE` on `reservoir_state` | Performs the same projection for reservoirs, with identical guard clauses for inferred end times and stale packets. |

The triggers only run after a successful history write/update, so any manual migration or remediation that touches the hypertables automatically refreshes the current view.

## 3. One-row-per-cycle enforcement

- The hypertables use `(irrigation_plan, start_time)` and `(reservoir, start_time)` as primary keys. That key, together with the upsert logic in the mutations, ensures only one history row exists per cycle.
- Conflict handlers compare packet timestamps and flags so that:
  - Older packets for the same cycle are ignored.
  - The first non-null `end_time` wins.
  - A definitive `end_time` replaces an inferred one exactly once.

## 4. Operational guidance

- **Backfills / corrections**: Insert or update rows in the hypertables only. The triggers will adjust the `current_*` tables automatically.
- **Schema changes**: Update both the hypertable migrations and the projection trigger migration together. Keep Timescale policies (compression, retention) attached to the history tables.
- **Testing**: Unit tests that validate inference or guard logic live next to the calculators (`packages/mqtt-integration/tests/irriganet`). Add regression coverage there whenever the packet interpretation rules change.

Refer back to this document before altering the MQTT mutations or the associated migrations to maintain the invariant that every cycle has exactly one history row and exactly one derived current row.
