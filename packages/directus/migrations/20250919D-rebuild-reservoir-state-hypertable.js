/**
 * Rebuild reservoir_state hypertable to use (reservoir, start_time) as PK/time dimension
 * and add inferred end-time tracking.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.renameTable("reservoir_state", "reservoir_state_old");

    await tx.raw(
      `ALTER INDEX IF EXISTS reservoir_state_pkey RENAME TO reservoir_state_old_pkey;`
    );

    await tx.schema.createTable("reservoir_state", (table) => {
      table.bigIncrements("id");
      table.uuid("reservoir").notNullable();
      table.timestamp("start_time").notNullable();
      table.timestamp("packet_date").notNullable();
      table.timestamp("restart_time").nullable();
      table.timestamp("end_time").nullable();
      table.boolean("end_time_inferred").notNullable().defaultTo(false);
      table.timestamp("date_created").notNullable().defaultTo(knex.fn.now());

      table
        .foreign("reservoir")
        .references("id")
        .inTable("reservoir");

      table.primary(["reservoir", "start_time"]);
    });

    await tx.raw(`CREATE EXTENSION IF NOT EXISTS timescaledb;`);

    await tx.raw(`
      SELECT create_hypertable(
        'reservoir_state',
        'start_time',
        chunk_time_interval => INTERVAL '1 month',
        if_not_exists => TRUE
      );
    `);

    await tx.raw(`
      ALTER TABLE reservoir_state SET (
        timescaledb.compress,
        timescaledb.compress_segmentby = 'reservoir'
      );
    `);

    await tx.raw(`
      SELECT add_compression_policy(
        'reservoir_state',
        INTERVAL '3 months'
      );
    `);

    await tx.raw(`
      COMMENT ON TABLE reservoir_state IS 'Historical reservoir automation state per cycle - TimescaleDB hypertable keyed by (reservoir, start_time)';
    `);

    await tx.raw(`
      COMMENT ON COLUMN reservoir_state.id IS 'Surrogate identifier retained for compatibility';
      COMMENT ON COLUMN reservoir_state.reservoir IS 'Foreign key to reservoir table';
      COMMENT ON COLUMN reservoir_state.start_time IS 'Cycle start timestamp - hypertable time dimension';
      COMMENT ON COLUMN reservoir_state.packet_date IS 'Packet reception timestamp for the automation report';
      COMMENT ON COLUMN reservoir_state.restart_time IS 'Timestamp when the automation restarted (nullable)';
      COMMENT ON COLUMN reservoir_state.end_time IS 'Timestamp when the automation ended (nullable)';
      COMMENT ON COLUMN reservoir_state.end_time_inferred IS 'True when end_time was inferred from packet_date due to missing telemetry';
      COMMENT ON COLUMN reservoir_state.date_created IS 'Insertion timestamp for the history row';
    `);

    await tx.raw(`
      INSERT INTO reservoir_state (
        id,
        reservoir,
        start_time,
        packet_date,
        restart_time,
        end_time,
        end_time_inferred,
        date_created
      )
      OVERRIDING SYSTEM VALUE
      SELECT
        id,
        reservoir,
        normalized_start_time AS start_time,
        packet_date,
        restart_time,
        end_time,
        CASE
          WHEN end_time IS NULL THEN FALSE
          WHEN end_time = packet_date THEN TRUE
          ELSE FALSE
        END AS end_time_inferred,
        date_created
      FROM (
        SELECT
          rs_old.*,
          COALESCE(rs_old.start_time, rs_old.packet_date) AS normalized_start_time,
          ROW_NUMBER() OVER (
            PARTITION BY reservoir, COALESCE(start_time, packet_date)
            ORDER BY
              CASE WHEN end_time IS NOT NULL THEN 0 ELSE 1 END,
              packet_date DESC,
              id DESC
          ) AS rn
        FROM reservoir_state_old rs_old
      ) ranked
      WHERE rn = 1;
    `);

    await tx.schema.dropTable("reservoir_state_old");
  });
}

/**
 * Roll back to the previous reservoir_state structure keyed by packet_date.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.renameTable("reservoir_state", "reservoir_state_new");

    await tx.raw(
      `ALTER INDEX IF EXISTS reservoir_state_pkey RENAME TO reservoir_state_new_pkey;`
    );

    await tx.schema.createTable("reservoir_state", (table) => {
      table.bigIncrements("id");
      table.uuid("reservoir").notNullable();
      table.timestamp("packet_date").notNullable();
      table.timestamp("start_time").nullable();
      table.timestamp("restart_time").nullable();
      table.timestamp("end_time").nullable();
      table.timestamp("date_created").notNullable().defaultTo(knex.fn.now());

      table
        .foreign("reservoir")
        .references("id")
        .inTable("reservoir");

      table.primary(["reservoir", "packet_date"]);
    });

    await tx.raw(`CREATE EXTENSION IF NOT EXISTS timescaledb;`);

    await tx.raw(`
      SELECT create_hypertable(
        'reservoir_state',
        'packet_date',
        chunk_time_interval => INTERVAL '1 month',
        if_not_exists => TRUE
      );
    `);

    await tx.raw(`
      ALTER TABLE reservoir_state SET (
        timescaledb.compress,
        timescaledb.compress_segmentby = 'reservoir'
      );
    `);

    await tx.raw(`
      SELECT add_compression_policy(
        'reservoir_state',
        INTERVAL '3 months'
      );
    `);

    await tx.raw(`
      INSERT INTO reservoir_state (
        id,
        reservoir,
        packet_date,
        start_time,
        restart_time,
        end_time,
        date_created
      )
      OVERRIDING SYSTEM VALUE
      SELECT
        id,
        reservoir,
        packet_date,
        start_time,
        restart_time,
        end_time,
        date_created
      FROM reservoir_state_new;
    `);

    await tx.schema.dropTable("reservoir_state_new");
  });
}
