/**
 * Align current_reservoir_state projection with reservoir_state one-row-per-cycle semantics
 * and add inferred end-time tracking.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(`DROP TRIGGER IF EXISTS reservoir_state_upsert_trigger ON current_reservoir_state;`);
    await tx.raw(`DROP TRIGGER IF EXISTS reservoir_state_update_trigger ON current_reservoir_state;`);
    await tx.raw(`DROP TRIGGER IF EXISTS update_current_reservoir_state_date_updated_trigger ON current_reservoir_state;`);
    await tx.raw(`DROP TRIGGER IF EXISTS set_reservoir_state_id_trigger ON current_reservoir_state;`);

    await tx.raw(`DROP FUNCTION IF EXISTS handle_reservoir_state_upsert();`);
    await tx.raw(`DROP FUNCTION IF EXISTS handle_reservoir_state_update();`);
    await tx.raw(`DROP FUNCTION IF EXISTS update_current_reservoir_state_date_updated();`);
    await tx.raw(`DROP FUNCTION IF EXISTS set_reservoir_state_id();`);

    await tx.schema.alterTable("current_reservoir_state", (table) => {
      table
        .boolean("end_time_inferred")
        .notNullable()
        .defaultTo(false);
    });

    await tx.raw(`
      COMMENT ON COLUMN current_reservoir_state.end_time_inferred IS 'True when the mirrored end_time was inferred from packet_date';
    `);

    await tx.raw(`DROP TRIGGER IF EXISTS sync_current_reservoir_state_trigger ON reservoir_state;`);
    await tx.raw(`DROP FUNCTION IF EXISTS sync_current_reservoir_state();`);

    await tx.raw(`
      CREATE OR REPLACE FUNCTION sync_current_reservoir_state()
      RETURNS TRIGGER AS $$
      BEGIN
        INSERT INTO current_reservoir_state (
          id,
          reservoir,
          packet_date,
          start_time,
          restart_time,
          end_time,
          end_time_inferred,
          date_created,
          date_updated
        )
        VALUES (
          NEW.reservoir,
          NEW.reservoir,
          NEW.packet_date,
          NEW.start_time,
          NEW.restart_time,
          NEW.end_time,
          COALESCE(NEW.end_time_inferred, FALSE),
          COALESCE(NEW.date_created, NOW()),
          NOW()
        )
        ON CONFLICT (reservoir) DO UPDATE
        SET
          id = EXCLUDED.id,
          start_time = CASE
            WHEN EXCLUDED.start_time > current_reservoir_state.start_time THEN EXCLUDED.start_time
            ELSE current_reservoir_state.start_time
          END,
          packet_date = CASE
            WHEN EXCLUDED.start_time > current_reservoir_state.start_time THEN EXCLUDED.packet_date
            WHEN EXCLUDED.start_time = current_reservoir_state.start_time
              AND EXCLUDED.packet_date > current_reservoir_state.packet_date THEN EXCLUDED.packet_date
            ELSE current_reservoir_state.packet_date
          END,
          restart_time = CASE
            WHEN EXCLUDED.start_time > current_reservoir_state.start_time THEN EXCLUDED.restart_time
            WHEN EXCLUDED.start_time = current_reservoir_state.start_time
              AND EXCLUDED.packet_date > current_reservoir_state.packet_date THEN EXCLUDED.restart_time
            ELSE current_reservoir_state.restart_time
          END,
          end_time = CASE
            WHEN EXCLUDED.start_time > current_reservoir_state.start_time THEN EXCLUDED.end_time
            WHEN EXCLUDED.start_time = current_reservoir_state.start_time
              AND current_reservoir_state.end_time IS NULL THEN EXCLUDED.end_time
            WHEN EXCLUDED.start_time = current_reservoir_state.start_time
              AND current_reservoir_state.end_time_inferred
              AND EXCLUDED.end_time IS NOT NULL
              AND NOT EXCLUDED.end_time_inferred THEN EXCLUDED.end_time
            ELSE current_reservoir_state.end_time
          END,
          end_time_inferred = CASE
            WHEN EXCLUDED.start_time > current_reservoir_state.start_time THEN EXCLUDED.end_time_inferred
            WHEN EXCLUDED.start_time = current_reservoir_state.start_time
              AND current_reservoir_state.end_time IS NULL
              AND EXCLUDED.end_time IS NOT NULL THEN EXCLUDED.end_time_inferred
            WHEN EXCLUDED.start_time = current_reservoir_state.start_time
              AND current_reservoir_state.end_time_inferred
              AND EXCLUDED.end_time IS NOT NULL
              AND NOT EXCLUDED.end_time_inferred THEN FALSE
            ELSE current_reservoir_state.end_time_inferred
          END,
          date_created = CASE
            WHEN EXCLUDED.start_time > current_reservoir_state.start_time THEN COALESCE(EXCLUDED.date_created, NOW())
            ELSE current_reservoir_state.date_created
          END,
          date_updated = CASE
            WHEN EXCLUDED.start_time > current_reservoir_state.start_time THEN NOW()
            WHEN EXCLUDED.start_time = current_reservoir_state.start_time
              AND EXCLUDED.packet_date > current_reservoir_state.packet_date THEN NOW()
            WHEN EXCLUDED.start_time = current_reservoir_state.start_time
              AND current_reservoir_state.end_time IS NULL
              AND EXCLUDED.end_time IS NOT NULL THEN NOW()
            WHEN EXCLUDED.start_time = current_reservoir_state.start_time
              AND current_reservoir_state.end_time_inferred
              AND EXCLUDED.end_time IS NOT NULL
              AND NOT EXCLUDED.end_time_inferred THEN NOW()
            ELSE current_reservoir_state.date_updated
          END
        WHERE
          EXCLUDED.start_time > current_reservoir_state.start_time
          OR (
            EXCLUDED.start_time = current_reservoir_state.start_time
            AND EXCLUDED.packet_date > current_reservoir_state.packet_date
          )
          OR (
            EXCLUDED.start_time = current_reservoir_state.start_time
            AND current_reservoir_state.end_time IS NULL
            AND EXCLUDED.end_time IS NOT NULL
          )
          OR (
            EXCLUDED.start_time = current_reservoir_state.start_time
            AND current_reservoir_state.end_time_inferred
            AND EXCLUDED.end_time IS NOT NULL
            AND NOT EXCLUDED.end_time_inferred
          );

        RETURN NULL;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER sync_current_reservoir_state_trigger
      AFTER INSERT OR UPDATE ON reservoir_state
      FOR EACH ROW
      EXECUTE FUNCTION sync_current_reservoir_state();
    `);
  });
}

/**
 * Restore previous current_reservoir_state trigger behaviour keyed by packet_date.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(`DROP TRIGGER IF EXISTS sync_current_reservoir_state_trigger ON reservoir_state;`);
    await tx.raw(`DROP FUNCTION IF EXISTS sync_current_reservoir_state();`);

    await tx.schema.alterTable("current_reservoir_state", (table) => {
      table.dropColumn("end_time_inferred");
    });

    await tx.raw(`
      CREATE OR REPLACE FUNCTION set_reservoir_state_id()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.id = NEW.reservoir;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER set_reservoir_state_id_trigger
      BEFORE INSERT ON current_reservoir_state
      FOR EACH ROW
      EXECUTE FUNCTION set_reservoir_state_id();
    `);

    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_reservoir_state_upsert()
      RETURNS TRIGGER AS $$
      DECLARE
        existing_record RECORD;
      BEGIN
        BEGIN
          INSERT INTO reservoir_state (
            reservoir,
            packet_date,
            start_time,
            restart_time,
            end_time,
            date_created
          ) VALUES (
            NEW.reservoir,
            NEW.packet_date,
            NEW.start_time,
            NEW.restart_time,
            NEW.end_time,
            NEW.date_created
          );
        EXCEPTION WHEN unique_violation THEN
          RAISE WARNING 'Duplicate key for reservoir % and packet_date % ignored in reservoir_state', NEW.reservoir, NEW.packet_date;
        END;

        IF TG_OP = 'INSERT' THEN
          SELECT * INTO existing_record
          FROM current_reservoir_state
          WHERE reservoir = NEW.reservoir;

          IF FOUND THEN
            IF (NEW.start_time IS NOT DISTINCT FROM existing_record.start_time) THEN
              IF existing_record.end_time IS NULL AND NEW.end_time IS NOT NULL THEN
                UPDATE current_reservoir_state
                SET end_time = NEW.end_time,
                    date_updated = NOW()
                WHERE reservoir = NEW.reservoir;
              END IF;
              RETURN NULL;
            ELSE
              DELETE FROM current_reservoir_state WHERE reservoir = NEW.reservoir;
              RETURN NEW;
            END IF;
          END IF;
        END IF;

        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER reservoir_state_upsert_trigger
      BEFORE INSERT ON current_reservoir_state
      FOR EACH ROW
      EXECUTE FUNCTION handle_reservoir_state_upsert();
    `);

    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_reservoir_state_update()
      RETURNS TRIGGER AS $$
      BEGIN
        BEGIN
          INSERT INTO reservoir_state (
            reservoir,
            packet_date,
            start_time,
            restart_time,
            end_time,
            date_created
          ) VALUES (
            NEW.reservoir,
            NEW.packet_date,
            NEW.start_time,
            NEW.restart_time,
            NEW.end_time,
            NOW()
          );
        EXCEPTION WHEN unique_violation THEN
          RAISE WARNING 'Duplicate key for reservoir % and packet_date % ignored in reservoir_state during update', NEW.reservoir, NEW.packet_date;
        END;

        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER reservoir_state_update_trigger
      AFTER UPDATE ON current_reservoir_state
      FOR EACH ROW
      EXECUTE FUNCTION handle_reservoir_state_update();
    `);

    await tx.raw(`
      CREATE OR REPLACE FUNCTION update_current_reservoir_state_date_updated()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.date_updated = NOW();
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER update_current_reservoir_state_date_updated_trigger
      BEFORE UPDATE ON current_reservoir_state
      FOR EACH ROW
      EXECUTE FUNCTION update_current_reservoir_state_date_updated();
    `);
  });
}
