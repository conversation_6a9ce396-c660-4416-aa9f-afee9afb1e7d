/**
 * Register end_time_inferred column for reservoir state collections in Directus.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.batchInsert("directus_fields", [
      {
        collection: "current_reservoir_state",
        field: "end_time_inferred",
        special: null,
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: JSON.stringify({
          iconOff: "close",
          iconOn: "check",
          labelOff: "Actual",
          labelOn: "Inferred",
        }),
        readonly: false,
        hidden: false,
        sort: 6,
        width: "third",
        translations: null,
        note: "Flags when the mirrored end_time was inferred from the automation report",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "reservoir_state",
        field: "end_time_inferred",
        special: null,
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: JSON.stringify({
          iconOff: "close",
          iconOn: "check",
          labelOff: "Actual",
          labelOn: "Inferred",
        }),
        readonly: true,
        hidden: false,
        sort: 6,
        width: "third",
        translations: null,
        note: "True when the stored end_time was inferred from packet_date",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    await tx("directus_permissions")
      .where({ collection: "current_reservoir_state", action: "create", policy: policyId })
      .update({
        fields:
          "reservoir,packet_date,start_time,restart_time,end_time,end_time_inferred",
      });

    await tx("directus_permissions")
      .where({ collection: "current_reservoir_state", action: "update", policy: policyId })
      .update({
        fields:
          "reservoir,packet_date,start_time,restart_time,end_time,end_time_inferred",
      });
  });
}

/**
 * Remove Directus metadata for reservoir end_time_inferred column.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx("directus_fields")
      .whereIn("collection", [
        "current_reservoir_state",
        "reservoir_state",
      ])
      .andWhere({ field: "end_time_inferred" })
      .delete();

    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    await tx("directus_permissions")
      .where({ collection: "current_reservoir_state", action: "create", policy: policyId })
      .update({
        fields: "reservoir,packet_date,start_time,restart_time,end_time",
      });

    await tx("directus_permissions")
      .where({ collection: "current_reservoir_state", action: "update", policy: policyId })
      .update({
        fields: "reservoir,packet_date,start_time,restart_time,end_time",
      });
  });
}
