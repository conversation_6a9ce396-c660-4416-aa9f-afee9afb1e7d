/**
 * Rebuild irrigation_plan_state hypertable to use (irrigation_plan, start_time) as PK/time dimension
 * and add inferred end-time tracking.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Detach trigger before manipulating the hypertable structure
    await tx.raw(
      `DROP TRIGGER IF EXISTS sync_current_irrigation_plan_state_trigger ON irrigation_plan_state;`
    );

    // Preserve existing data under a temporary name for the migration window
    await tx.schema.renameTable(
      "irrigation_plan_state",
      "irrigation_plan_state_old"
    );

    // Ensure legacy primary key/index names do not collide with the new table
    await tx.raw(
      `ALTER INDEX IF EXISTS irrigation_plan_state_pkey RENAME TO irrigation_plan_state_old_pkey;`
    );

    // Recreate the hypertable with the new structure (start_time as primary key/time dimension)
    await tx.schema.createTable("irrigation_plan_state", (table) => {
      table.bigIncrements("id");
      table.uuid("irrigation_plan").notNullable();
      table.timestamp("start_time").notNullable();
      table.timestamp("packet_date").notNullable();
      table.timestamp("end_time").nullable();
      table.boolean("end_time_inferred").notNullable().defaultTo(false);
      table.jsonb("activated_steps").notNullable();
      table.jsonb("activated_ferti_steps").notNullable();
      table.boolean("waterpump_working").notNullable();
      table.timestamp("backwash_start_time").nullable();
      table.boolean("uses_waterpump").notNullable();
      table.boolean("uses_ferti").notNullable();
      table.timestamp("date_created").notNullable().defaultTo(knex.fn.now());

      table
        .foreign("irrigation_plan")
        .references("id")
        .inTable("irrigation_plan");

      table.primary(["irrigation_plan", "start_time"]);
    });

    await tx.raw(`CREATE EXTENSION IF NOT EXISTS timescaledb;`);

    await tx.raw(`
      SELECT create_hypertable(
        'irrigation_plan_state',
        'start_time',
        chunk_time_interval => INTERVAL '1 month',
        if_not_exists => TRUE
      );
    `);

    await tx.raw(`
      ALTER TABLE irrigation_plan_state SET (
        timescaledb.compress,
        timescaledb.compress_segmentby = 'irrigation_plan'
      );
    `);

    await tx.raw(`
      SELECT add_compression_policy(
        'irrigation_plan_state',
        INTERVAL '3 months'
      );
    `);

    await tx.raw(`
      COMMENT ON TABLE irrigation_plan_state IS 'Historical state of irrigation plan executions based on SchedulingReportPackage MQTT messages - TimescaleDB hypertable (one row per cycle)';
    `);

    await tx.raw(`
      COMMENT ON COLUMN irrigation_plan_state.id IS 'Surrogate identifier retained for compatibility';
      COMMENT ON COLUMN irrigation_plan_state.irrigation_plan IS 'Foreign key to irrigation_plan table';
      COMMENT ON COLUMN irrigation_plan_state.start_time IS 'Cycle start timestamp - hypertable time dimension';
      COMMENT ON COLUMN irrigation_plan_state.packet_date IS 'When the originating device packet was recorded';
      COMMENT ON COLUMN irrigation_plan_state.end_time IS 'When the cycle ended (null if still running)';
      COMMENT ON COLUMN irrigation_plan_state.end_time_inferred IS 'True when end_time was inferred from packet_date due to missing telemetry';
      COMMENT ON COLUMN irrigation_plan_state.activated_steps IS 'IDs of irrigation_plan_step records activated in the cycle';
      COMMENT ON COLUMN irrigation_plan_state.activated_ferti_steps IS 'IDs of irrigation_plan_step records with fertigation in the cycle';
      COMMENT ON COLUMN irrigation_plan_state.waterpump_working IS 'Whether the water pump reported active during the packet';
      COMMENT ON COLUMN irrigation_plan_state.backwash_start_time IS 'When the backwash started (nullable)';
      COMMENT ON COLUMN irrigation_plan_state.uses_waterpump IS 'Whether the irrigation plan should use the water pump';
      COMMENT ON COLUMN irrigation_plan_state.uses_ferti IS 'Whether the irrigation plan should perform fertigation';
      COMMENT ON COLUMN irrigation_plan_state.date_created IS 'Insertion timestamp for the history row';
    `);

    // Backfill data with deduplication by (irrigation_plan, start_time)
    await tx.raw(`
      INSERT INTO irrigation_plan_state (
        id,
        irrigation_plan,
        start_time,
        packet_date,
        end_time,
        end_time_inferred,
        activated_steps,
        activated_ferti_steps,
        waterpump_working,
        backwash_start_time,
        uses_waterpump,
        uses_ferti,
        date_created
      )
      OVERRIDING SYSTEM VALUE
      SELECT
        id,
        irrigation_plan,
        normalized_start_time AS start_time,
        packet_date,
        end_time,
        CASE
          WHEN end_time IS NULL THEN FALSE
          WHEN end_time = packet_date THEN TRUE
          ELSE FALSE
        END AS end_time_inferred,
        activated_steps,
        activated_ferti_steps,
        waterpump_working,
        backwash_start_time,
        uses_waterpump,
        uses_ferti,
        date_created
      FROM (
        SELECT
          ips_old.*,
          COALESCE(ips_old.start_time, ips_old.packet_date) AS normalized_start_time,
          ROW_NUMBER() OVER (
            PARTITION BY irrigation_plan, COALESCE(start_time, packet_date)
            ORDER BY
              CASE WHEN end_time IS NOT NULL THEN 0 ELSE 1 END,
              packet_date DESC,
              id DESC
          ) AS rn
        FROM irrigation_plan_state_old ips_old
      ) ranked
      WHERE rn = 1;
    `);

    // Reattach trigger to keep current_irrigation_plan_state synchronized
    await tx.raw(`
      CREATE TRIGGER sync_current_irrigation_plan_state_trigger
      AFTER INSERT ON irrigation_plan_state
      FOR EACH ROW
      EXECUTE FUNCTION sync_current_irrigation_plan_state();
    `);

    // Drop the old table once migration is successful
    await tx.schema.dropTable("irrigation_plan_state_old");
  });
}

/**
 * Roll back to the previous structure (packet_date primary key/time dimension).
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(
      `DROP TRIGGER IF EXISTS sync_current_irrigation_plan_state_trigger ON irrigation_plan_state;`
    );

    await tx.schema.renameTable(
      "irrigation_plan_state",
      "irrigation_plan_state_new"
    );

    await tx.raw(
      `ALTER INDEX IF EXISTS irrigation_plan_state_pkey RENAME TO irrigation_plan_state_new_pkey;`
    );

    await tx.schema.createTable("irrigation_plan_state", (table) => {
      table.bigIncrements("id");
      table.uuid("irrigation_plan").notNullable();
      table.timestamp("packet_date").notNullable();
      table.timestamp("start_time").nullable();
      table.timestamp("end_time").nullable();
      table.jsonb("activated_steps").notNullable();
      table.jsonb("activated_ferti_steps").notNullable();
      table.boolean("waterpump_working").notNullable();
      table.timestamp("backwash_start_time").nullable();
      table.boolean("uses_waterpump").notNullable();
      table.boolean("uses_ferti").notNullable();
      table.timestamp("date_created").notNullable().defaultTo(knex.fn.now());

      table
        .foreign("irrigation_plan")
        .references("id")
        .inTable("irrigation_plan");

      table.primary(["irrigation_plan", "packet_date"]);
    });

    await tx.raw(`CREATE EXTENSION IF NOT EXISTS timescaledb;`);

    await tx.raw(`
      SELECT create_hypertable(
        'irrigation_plan_state',
        'packet_date',
        chunk_time_interval => INTERVAL '1 month',
        if_not_exists => TRUE
      );
    `);

    await tx.raw(`
      ALTER TABLE irrigation_plan_state SET (
        timescaledb.compress,
        timescaledb.compress_segmentby = 'irrigation_plan'
      );
    `);

    await tx.raw(`
      SELECT add_compression_policy(
        'irrigation_plan_state',
        INTERVAL '3 months'
      );
    `);

    await tx.raw(`
      INSERT INTO irrigation_plan_state (
        id,
        irrigation_plan,
        packet_date,
        start_time,
        end_time,
        activated_steps,
        activated_ferti_steps,
        waterpump_working,
        backwash_start_time,
        uses_waterpump,
        uses_ferti,
        date_created
      )
      OVERRIDING SYSTEM VALUE
      SELECT
        id,
        irrigation_plan,
        packet_date,
        start_time,
        end_time,
        activated_steps,
        activated_ferti_steps,
        waterpump_working,
        backwash_start_time,
        uses_waterpump,
        uses_ferti,
        date_created
      FROM irrigation_plan_state_new;
    `);

    await tx.raw(`
      CREATE TRIGGER sync_current_irrigation_plan_state_trigger
      AFTER INSERT ON irrigation_plan_state
      FOR EACH ROW
      EXECUTE FUNCTION sync_current_irrigation_plan_state();
    `);

    await tx.schema.dropTable("irrigation_plan_state_new");
  });
}
