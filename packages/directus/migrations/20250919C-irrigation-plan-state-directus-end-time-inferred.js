/**
 * Register end_time_inferred column in Directus metadata for irrigation plan states.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.batchInsert("directus_fields", [
      {
        collection: "current_irrigation_plan_state",
        field: "end_time_inferred",
        special: null,
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: JSON.stringify({
          iconOff: "close",
          iconOn: "check",
          labelOff: "Actual",
          labelOn: "Inferred",
        }),
        readonly: false,
        hidden: false,
        sort: 6,
        width: "third",
        translations: null,
        note: "Flags when the end_time was inferred from the packet timestamp",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "irrigation_plan_state",
        field: "end_time_inferred",
        special: null,
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: JSON.stringify({
          iconOff: "close",
          iconOn: "check",
          labelOff: "Actual",
          labelOn: "Inferred",
        }),
        readonly: false,
        hidden: false,
        sort: 6,
        width: "third",
        translations: null,
        note: "True when the stored end_time was inferred from packet_date",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    await tx("directus_permissions")
      .where({ collection: "current_irrigation_plan_state", action: "create" })
      .update({
        fields:
          "irrigation_plan,packet_date,start_time,end_time,end_time_inferred,activated_steps,activated_ferti_steps,waterpump_working,backwash_start_time,uses_waterpump,uses_ferti",
      });

    await tx("directus_permissions")
      .where({ collection: "current_irrigation_plan_state", action: "update" })
      .update({
        fields:
          "irrigation_plan,packet_date,start_time,end_time,end_time_inferred,activated_steps,activated_ferti_steps,waterpump_working,backwash_start_time,uses_waterpump,uses_ferti",
      });
  });
}

/**
 * Remove Directus metadata entries for end_time_inferred.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx("directus_fields")
      .whereIn("collection", [
        "current_irrigation_plan_state",
        "irrigation_plan_state",
      ])
      .andWhere({ field: "end_time_inferred" })
      .delete();

    await tx("directus_permissions")
      .where({ collection: "current_irrigation_plan_state", action: "create" })
      .update({
        fields:
          "irrigation_plan,packet_date,start_time,end_time,activated_steps,activated_ferti_steps,waterpump_working,backwash_start_time,uses_waterpump,uses_ferti",
      });

    await tx("directus_permissions")
      .where({ collection: "current_irrigation_plan_state", action: "update" })
      .update({
        fields:
          "irrigation_plan,packet_date,start_time,end_time,activated_steps,activated_ferti_steps,waterpump_working,backwash_start_time,uses_waterpump,uses_ferti",
      });
  });
}
