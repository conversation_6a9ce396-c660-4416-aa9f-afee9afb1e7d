import { EventEmitter } from 'events';
import { Binding, BindingState } from './binding';
import type { BindingConfig } from '../types';
import { logger } from '../logger';

/**
 * Manager states during lifecycle
 */
export enum ManagerState {
  STOPPED = 'stopped',
  STARTING = 'starting',
  RUNNING = 'running',
  STOPPING = 'stopping',
  ERROR = 'error'
}

/**
 * Events emitted by the BindingManager
 */
export interface BindingManagerEvents {
  'state-changed': (oldState: ManagerState, newState: ManagerState) => void;
  'binding-started': (bindingName: string) => void;
  'binding-stopped': (bindingName: string) => void;
  'binding-error': (bindingName: string, error: Error) => void;
  'message-forwarded': (bindingName: string, sourceTopic: string, targetTopic: string, qos: number) => void;
  'error': (error: Error) => void;
}

/**
 * Statistics for monitoring binding performance
 */
export interface BindingStats {
  bindingName: string;
  state: BindingState;
  messagesForwarded: number;
  errorCount: number;
  lastMessageTime?: Date;
  lastErrorTime?: Date;
}

/**
 * BindingManager coordinates multiple bindings simultaneously and manages
 * their independent operation, providing a unified interface for starting
 * and stopping all bindings.
 */
export class BindingManager extends EventEmitter {
  private bindings: Map<string, Binding> = new Map();
  private state: ManagerState = ManagerState.STOPPED;
  private stats: Map<string, BindingStats> = new Map();

  constructor() {
    super();
  }

  /**
   * Get the current state of the manager
   */
  get currentState(): ManagerState {
    return this.state;
  }

  /**
   * Get the number of managed bindings
   */
  get bindingCount(): number {
    return this.bindings.size;
  }

  /**
   * Get list of all binding names
   */
  get bindingNames(): string[] {
    return Array.from(this.bindings.keys());
  }

  /**
   * Check if manager is running
   */
  get isRunning(): boolean {
    return this.state === ManagerState.RUNNING;
  }

  /**
   * Add a binding configuration to the manager
   */
  addBinding(config: BindingConfig): void {
    if (this.state !== ManagerState.STOPPED) {
      throw new Error('Cannot add bindings while manager is running');
    }

    const binding = new Binding(config);
    const bindingName = binding.name;

    if (this.bindings.has(bindingName)) {
      throw new Error(`Binding with name '${bindingName}' already exists`);
    }

    this.bindings.set(bindingName, binding);
    this.setupBindingHandlers(binding);
    this.initializeStats(bindingName);

    logger.info(`Binding '${bindingName}' added to manager`);
  }

  /**
   * Remove a binding from the manager
   */
  async removeBinding(bindingName: string): Promise<void> {
    if (this.state !== ManagerState.STOPPED) {
      throw new Error('Cannot remove bindings while manager is running');
    }

    const binding = this.bindings.get(bindingName);
    if (!binding) {
      throw new Error(`Binding '${bindingName}' not found`);
    }

    // Ensure binding is stopped before removing
    if (binding.isRunning) {
      await binding.stop();
    }

    this.bindings.delete(bindingName);
    this.stats.delete(bindingName);

    logger.info(`Binding '${bindingName}' removed from manager`);
  }

  /**
   * Start all bindings
   */
  async startAll(): Promise<void> {
    if (this.state !== ManagerState.STOPPED) {
      logger.warn(`Manager is not in stopped state, current state: ${this.state}`);
      return;
    }

    if (this.bindings.size === 0) {
      throw new Error('No bindings configured');
    }

    this.setState(ManagerState.STARTING);
    logger.info(`Starting ${this.bindings.size} bindings`);

    const startPromises: Array<Promise<void>> = [];
    const errors: Error[] = [];

    // Start all bindings concurrently
    for (const [bindingName, binding] of this.bindings) {
      const startPromise = binding.start().catch(error => {
        errors.push(new Error(`Failed to start binding '${bindingName}': ${error.message}`));
      });
      startPromises.push(startPromise);
    }

    // Wait for all bindings to start
    await Promise.all(startPromises);

    // Check if any bindings failed to start
    if (errors.length > 0) {
      this.setState(ManagerState.ERROR);
      const errorMsg = `Failed to start ${errors.length} binding(s): ${errors.map(e => e.message).join('; ')}`;
      logger.error(errorMsg);

      // Stop any bindings that did start
      await this.stopAllBindings();

      const managerError = new Error(errorMsg);
      this.emit('error', managerError);
      throw managerError;
    }

    this.setState(ManagerState.RUNNING);
    logger.info('All bindings started successfully');
  }

  /**
   * Stop all bindings
   */
  async stopAll(): Promise<void> {
    if (this.state === ManagerState.STOPPED || this.state === ManagerState.STOPPING) {
      logger.debug('Manager already stopping or stopped');
      return;
    }

    this.setState(ManagerState.STOPPING);
    logger.info('Stopping all bindings');

    await this.stopAllBindings();

    this.setState(ManagerState.STOPPED);
    logger.info('All bindings stopped');
  }

  /**
   * Get statistics for all bindings
   */
  getStats(): BindingStats[] {
    return Array.from(this.stats.values());
  }

  /**
   * Get statistics for a specific binding
   */
  getBindingStats(bindingName: string): BindingStats | undefined {
    return this.stats.get(bindingName);
  }

  /**
   * Get the state of a specific binding
   */
  getBindingState(bindingName: string): BindingState | undefined {
    const binding = this.bindings.get(bindingName);
    return binding?.currentState;
  }

  /**
   * Check if a specific binding is running
   */
  isBindingRunning(bindingName: string): boolean {
    const binding = this.bindings.get(bindingName);
    return binding?.isRunning || false;
  }

  /**
   * Setup event handlers for a binding
   */
  private setupBindingHandlers(binding: Binding): void {
    const bindingName = binding.name;

    binding.on('state-changed', (oldState, newState) => {
      this.updateBindingState(bindingName, newState);

      if (newState === BindingState.RUNNING) {
        this.emit('binding-started', bindingName);
      } else if (newState === BindingState.STOPPED) {
        this.emit('binding-stopped', bindingName);
      }
    });

    binding.on('message-forwarded', (sourceTopic, targetTopic, qos) => {
      this.incrementMessageCount(bindingName);
      this.emit('message-forwarded', bindingName, sourceTopic, targetTopic, qos);
      logger.debug(`[${bindingName}] Message forwarded: ${sourceTopic} -> ${targetTopic}`);
    });

    binding.on('error', (error) => {
      this.incrementErrorCount(bindingName);
      this.emit('binding-error', bindingName, error);
      logger.error(`[${bindingName}] Binding error: ${error.message}`);
    });
  }

  /**
   * Initialize statistics for a binding
   */
  private initializeStats(bindingName: string): void {
    this.stats.set(bindingName, {
      bindingName,
      state: BindingState.STOPPED,
      messagesForwarded: 0,
      errorCount: 0
    });
  }

  /**
   * Update binding state in statistics
   */
  private updateBindingState(bindingName: string, state: BindingState): void {
    const stats = this.stats.get(bindingName);
    if (stats) {
      stats.state = state;
    }
  }

  /**
   * Increment message count for a binding
   */
  private incrementMessageCount(bindingName: string): void {
    const stats = this.stats.get(bindingName);
    if (stats) {
      stats.messagesForwarded++;
      stats.lastMessageTime = new Date();
    }
  }

  /**
   * Increment error count for a binding
   */
  private incrementErrorCount(bindingName: string): void {
    const stats = this.stats.get(bindingName);
    if (stats) {
      stats.errorCount++;
      stats.lastErrorTime = new Date();
    }
  }

  /**
   * Stop all bindings (internal helper)
   */
  private async stopAllBindings(): Promise<void> {
    const stopPromises: Array<Promise<void>> = [];
    const errors: Error[] = [];

    // Stop all bindings concurrently
    for (const [bindingName, binding] of this.bindings) {
      const stopPromise = binding.stop().catch(error => {
        errors.push(new Error(`Failed to stop binding '${bindingName}': ${error.message}`));
      });
      stopPromises.push(stopPromise);
    }

    // Wait for all bindings to stop
    await Promise.all(stopPromises);

    // Log any errors that occurred during shutdown (but don't throw)
    if (errors.length > 0) {
      const errorMsg = `Errors occurred while stopping bindings: ${errors.map(e => e.message).join('; ')}`;
      logger.warn(errorMsg);
    }
  }

  /**
   * Update the manager state and emit change event
   */
  private setState(newState: ManagerState): void {
    if (this.state !== newState) {
      const oldState = this.state;
      this.state = newState;
      logger.debug(`BindingManager state changed: ${oldState} -> ${newState}`);
      this.emit('state-changed', oldState, newState);
    }
  }

  /**
   * Override EventEmitter methods to provide type safety
   */
  override on<K extends keyof BindingManagerEvents>(event: K, listener: BindingManagerEvents[K]): this {
    return super.on(event, listener);
  }

  override emit<K extends keyof BindingManagerEvents>(event: K, ...args: Parameters<BindingManagerEvents[K]>): boolean {
    return super.emit(event, ...args);
  }
}