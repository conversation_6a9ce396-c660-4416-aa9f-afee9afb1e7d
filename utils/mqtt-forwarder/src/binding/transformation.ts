/**
 * Topic transformation utilities for MQTT message forwarding
 */

/**
 * Transforms a source topic to a target topic using an optional template.
 *
 * Transformation rules:
 * - If no template is provided, returns the source topic unchanged
 * - If template contains ${topic}, replaces it with the source topic
 * - If template doesn't contain ${topic}, returns the template unchanged
 *
 * @param sourceTopic - The original topic from the source broker
 * @param targetTemplate - Optional template for target topic transformation
 * @returns The transformed target topic
 *
 * @example
 * ```typescript
 * transformTopic('a/b/c') // returns 'a/b/c'
 * transformTopic('a/b/c', 'x/y/${topic}') // returns 'x/y/a/b/c'
 * transformTopic('a/b/c', 'x/y/${topic}/z') // returns 'x/y/a/b/c/z'
 * transformTopic('a/b/c', 'x/y/z') // returns 'x/y/z'
 * ```
 */
export function transformTopic(sourceTopic: string, targetTemplate?: string): string {
  // If no template is provided, return source topic unchanged
  if (!targetTemplate) {
    return sourceTopic;
  }

  // Replace ${topic} placeholder with the source topic
  return targetTemplate.replace(/\$\{topic\}/g, sourceTopic);
}

/**
 * Validates that a topic transformation template is well-formed.
 *
 * @param template - The template to validate
 * @returns true if the template is valid, false otherwise
 */
export function validateTopicTemplate(template: string): boolean {
  if (!template) {
    return true; // Empty template is valid (means no transformation)
  }

  // Check for valid MQTT topic characters and ${topic} placeholder
  // MQTT topics should not contain null characters, and wildcards should be used carefully
  const invalidChars = /[\x00]/;
  if (invalidChars.test(template)) {
    return false;
  }

  // Check for malformed placeholders (${...} patterns)
  // First, check for any ${} patterns that are not properly closed
  if (template.includes('${') && !template.match(/\$\{[^}]*\}/)) {
    return false; // Contains ${ but no properly closed placeholder
  }

  // Check for incomplete placeholders like ${topic without closing }
  if (template.includes('${') && template.indexOf('}', template.indexOf('${')) === -1) {
    return false;
  }

  // Check for reversed patterns like $topic}
  if (template.includes('$') && template.includes('}') && !template.includes('${')) {
    // Look for patterns that might be malformed like $topic}
    if (/\$[^{][^}]*\}/.test(template)) {
      return false;
    }
  }

  // Validate that any complete ${...} placeholders are only ${topic}
  const placeholders = template.match(/\$\{[^}]*\}/g);
  if (placeholders) {
    return placeholders.every(placeholder => placeholder === '${topic}');
  }

  return true;
}