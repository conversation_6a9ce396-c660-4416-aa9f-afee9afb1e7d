import { EventEmitter } from 'events';
import { MQ<PERSON><PERSON>lientWrapper, type MQTTMessage } from '../mqtt/client';
import type { BindingConfig } from '../types';
import { logger } from '../logger';
import { transformTopic } from './transformation';

/**
 * Binding states during lifecycle
 */
export enum BindingState {
  STOPPED = 'stopped',
  STARTING = 'starting',
  RUNNING = 'running',
  STOPPING = 'stopping',
  ERROR = 'error'
}

/**
 * Events emitted by a Binding
 */
export interface BindingEvents {
  'state-changed': (oldState: BindingState, newState: BindingState) => void;
  'message-forwarded': (sourceTopic: string, targetTopic: string, qos: number) => void;
  'error': (error: Error) => void;
  'source-connected': () => void;
  'source-disconnected': () => void;
  'target-connected': () => void;
  'target-disconnected': () => void;
}

/**
 * Individual binding that manages source and target MQTT clients
 * and handles message forwarding for a single binding configuration.
 */
export class Binding extends EventEmitter {
  private config: BindingConfig;
  private sourceClient: MQTTClientWrapper;
  private targetClient: MQTTClientWrapper;
  private state: BindingState = BindingState.STOPPED;
  private bindingName: string;

  constructor(config: BindingConfig) {
    super();
    this.config = config;
    this.bindingName = config.name || `${config.source.brokerUrl}->${config.target.brokerUrl}`;

    // Create MQTT clients with binding-specific client IDs
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);

    this.sourceClient = new MQTTClientWrapper(
      config.source,
      `${this.bindingName}-source-${timestamp}-${random}`
    );

    this.targetClient = new MQTTClientWrapper(
      config.target,
      `${this.bindingName}-target-${timestamp}-${random}`
    );

    this.setupEventHandlers();
  }

  /**
   * Get the current state of this binding
   */
  get currentState(): BindingState {
    return this.state;
  }

  /**
   * Get the binding name for logging and identification
   */
  get name(): string {
    return this.bindingName;
  }

  /**
   * Get the binding configuration
   */
  get configuration(): BindingConfig {
    return { ...this.config }; // Return a copy to prevent modification
  }

  /**
   * Check if binding is running
   */
  get isRunning(): boolean {
    return this.state === BindingState.RUNNING;
  }

  /**
   * Start the binding - connect clients and begin forwarding
   */
  async start(): Promise<void> {
    if (this.state !== BindingState.STOPPED) {
      logger.warn(`[${this.bindingName}] Binding is not in stopped state, current state: ${this.state}`);
      return;
    }

    this.setState(BindingState.STARTING);
    logger.info(`[${this.bindingName}] Starting binding`);

    try {
      // Connect both clients
      logger.debug(`[${this.bindingName}] Connecting to source broker: ${this.config.source.brokerUrl}`);
      await this.sourceClient.connect();

      logger.debug(`[${this.bindingName}] Connecting to target broker: ${this.config.target.brokerUrl}`);
      await this.targetClient.connect();

      // Subscribe to source topic
      logger.debug(`[${this.bindingName}] Subscribing to source topic: ${this.config.source.topic}`);
      await this.sourceClient.subscribe(this.config.source.topic);

      this.setState(BindingState.RUNNING);
      logger.info(`[${this.bindingName}] Binding started successfully`);

    } catch (error) {
      this.setState(BindingState.ERROR);
      const errorMsg = `Failed to start binding: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(`[${this.bindingName}] ${errorMsg}`);

      // Cleanup on failure
      await this.cleanup();

      const bindingError = new Error(`[${this.bindingName}] ${errorMsg}`);
      this.emit('error', bindingError);
      throw bindingError;
    }
  }

  /**
   * Stop the binding - disconnect clients and stop forwarding
   */
  async stop(): Promise<void> {
    if (this.state === BindingState.STOPPED || this.state === BindingState.STOPPING) {
      logger.debug(`[${this.bindingName}] Binding already stopping or stopped`);
      return;
    }

    this.setState(BindingState.STOPPING);
    logger.info(`[${this.bindingName}] Stopping binding`);

    try {
      await this.cleanup();
      this.setState(BindingState.STOPPED);
      logger.info(`[${this.bindingName}] Binding stopped successfully`);
    } catch (error) {
      this.setState(BindingState.ERROR);
      const errorMsg = `Failed to stop binding cleanly: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(`[${this.bindingName}] ${errorMsg}`);
      throw new Error(`[${this.bindingName}] ${errorMsg}`);
    }
  }

  /**
   * Setup event handlers for both MQTT clients
   */
  private setupEventHandlers(): void {
    // Source client events
    this.sourceClient.on('connected', () => {
      logger.debug(`[${this.bindingName}] Source client connected`);
      this.emit('source-connected');
    });

    this.sourceClient.on('disconnected', () => {
      logger.debug(`[${this.bindingName}] Source client disconnected`);
      this.emit('source-disconnected');
    });

    this.sourceClient.on('error', (error) => {
      logger.error(`[${this.bindingName}] Source client error: ${error.message}`);
      this.handleClientError('source', error);
    });

    this.sourceClient.on('message', (message) => {
      this.handleMessage(message);
    });

    // Target client events
    this.targetClient.on('connected', () => {
      logger.debug(`[${this.bindingName}] Target client connected`);
      this.emit('target-connected');
    });

    this.targetClient.on('disconnected', () => {
      logger.debug(`[${this.bindingName}] Target client disconnected`);
      this.emit('target-disconnected');
    });

    this.targetClient.on('error', (error) => {
      logger.error(`[${this.bindingName}] Target client error: ${error.message}`);
      this.handleClientError('target', error);
    });
  }

  /**
   * Handle incoming messages from the source broker
   */
  private async handleMessage(message: MQTTMessage): Promise<void> {
    if (this.state !== BindingState.RUNNING) {
      logger.debug(`[${this.bindingName}] Ignoring message - binding not running (state: ${this.state})`);
      return;
    }

    try {
      // Transform the topic
      const targetTopic = transformTopic(message.topic, this.config.target.topic);

      logger.debug(`[${this.bindingName}] Forwarding message: ${message.topic} -> ${targetTopic} (QoS: ${message.qos}, retain: ${message.retain})`);

      // Forward the message to target broker
      await this.targetClient.publish(
        targetTopic,
        message.payload,
        message.qos,
        message.retain
      );

      // Emit forwarding event for monitoring
      this.emit('message-forwarded', message.topic, targetTopic, message.qos);

      logger.debug(`[${this.bindingName}] Message forwarded successfully`);

    } catch (error) {
      const errorMsg = `Failed to forward message from ${message.topic}: ${error instanceof Error ? error.message : String(error)}`;
      logger.error(`[${this.bindingName}] ${errorMsg}`);

      // Don't crash the binding on individual message errors
      // Just log and continue processing other messages
      this.emit('error', new Error(`[${this.bindingName}] ${errorMsg}`));
    }
  }

  /**
   * Handle errors from MQTT clients
   */
  private handleClientError(clientType: 'source' | 'target', error: Error): void {
    const errorMsg = `${clientType} client error: ${error.message}`;
    logger.error(`[${this.bindingName}] ${errorMsg}`);

    // In a production system, you might want to implement more sophisticated
    // error recovery here (e.g., restart the binding after a delay)
    this.emit('error', new Error(`[${this.bindingName}] ${errorMsg}`));
  }

  /**
   * Update the binding state and emit change event
   */
  private setState(newState: BindingState): void {
    if (this.state !== newState) {
      const oldState = this.state;
      this.state = newState;
      logger.debug(`[${this.bindingName}] State changed: ${oldState} -> ${newState}`);
      this.emit('state-changed', oldState, newState);
    }
  }

  /**
   * Clean up resources - disconnect clients
   */
  private async cleanup(): Promise<void> {
    const cleanupPromises: Promise<void>[] = [];

    // Disconnect source client
    if (this.sourceClient) {
      cleanupPromises.push(
        this.sourceClient.disconnect().catch(error => {
          logger.warn(`[${this.bindingName}] Error disconnecting source client: ${error.message}`);
        })
      );
    }

    // Disconnect target client
    if (this.targetClient) {
      cleanupPromises.push(
        this.targetClient.disconnect().catch(error => {
          logger.warn(`[${this.bindingName}] Error disconnecting target client: ${error.message}`);
        })
      );
    }

    // Wait for all disconnections to complete
    await Promise.all(cleanupPromises);
    logger.debug(`[${this.bindingName}] Cleanup completed`);
  }

  /**
   * Override EventEmitter methods to provide type safety
   */
  override on<K extends keyof BindingEvents>(event: K, listener: BindingEvents[K]): this {
    return super.on(event, listener);
  }

  override emit<K extends keyof BindingEvents>(event: K, ...args: Parameters<BindingEvents[K]>): boolean {
    return super.emit(event, ...args);
  }
}