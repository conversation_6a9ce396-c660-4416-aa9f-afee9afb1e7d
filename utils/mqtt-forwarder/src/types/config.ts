/**
 * MQTT broker connection configuration
 */
export interface BrokerConfig {
  brokerUrl: string;
  username?: string;
  password?: string;
}

/**
 * Source broker configuration with topic subscription
 */
export interface SourceConfig extends BrokerConfig {
  topic: string;
}

/**
 * Target broker configuration with optional topic transformation template
 */
export interface TargetConfig extends BrokerConfig {
  topic?: string;
}

/**
 * Binding configuration that defines a source-to-target mapping
 */
export interface BindingConfig {
  name?: string;
  source: SourceConfig;
  target: TargetConfig;
}

/**
 * Application log levels
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * Main application configuration
 */
export interface AppConfig {
  bindings: BindingConfig[];
  logLevel?: LogLevel;
}