import pino from 'pino';
import type { LogLevel } from './types';

/**
 * Creates a configured Pino logger instance
 * @param logLevel - The log level to use (defaults to 'info')
 * @returns Configured Pino logger
 */
export function createLogger(logLevel: LogLevel = 'info') {
  return pino({
    level: logLevel,
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'yyyy-mm-dd HH:MM:ss',
        ignore: 'pid,hostname'
      }
    }
  });
}

/**
 * Default logger instance
 * This will be reconfigured when the application loads configuration
 */
export let logger = createLogger('info');

/**
 * Updates the global logger instance with a new log level
 * @param logLevel - The new log level to use
 */
export function updateLogLevel(logLevel: LogLevel) {
  logger = createLogger(logLevel);
}