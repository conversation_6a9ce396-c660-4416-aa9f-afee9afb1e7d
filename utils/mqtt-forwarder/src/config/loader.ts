import { readFileSync } from 'fs';
import type { AppConfig } from '../types';
import { logger } from '../logger';

/**
 * Error thrown when configuration file operations fail
 */
export class ConfigurationError extends Error {
  constructor(message: string, public override readonly cause?: Error) {
    super(message);
    this.name = 'ConfigurationError';
  }
}

/**
 * Reads and parses a JSON configuration file
 * @param filePath - Path to the configuration file
 * @returns Parsed configuration object
 * @throws ConfigurationError when file reading or parsing fails
 */
export function readConfigFile(filePath: string): unknown {
  try {
    logger.debug(`Reading configuration file: ${filePath}`);

    const fileContent = readFileSync(filePath, 'utf8');

    if (!fileContent.trim()) {
      throw new ConfigurationError(`Configuration file is empty: ${filePath}`);
    }

    const config = JSON.parse(fileContent);

    logger.debug('Configuration file parsed successfully');
    return config;

  } catch (error) {
    if (error instanceof ConfigurationError) {
      throw error;
    }

    if (error instanceof SyntaxError) {
      throw new ConfigurationError(
        `Invalid JSON syntax in configuration file: ${filePath}`,
        error
      );
    }

    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      throw new ConfigurationError(`Configuration file not found: ${filePath}`, error as Error);
    }

    if ((error as NodeJS.ErrnoException).code === 'EACCES') {
      throw new ConfigurationError(`Permission denied reading configuration file: ${filePath}`, error as Error);
    }

    throw new ConfigurationError(
      `Failed to read configuration file: ${filePath}`,
      error as Error
    );
  }
}

/**
 * Parses command line arguments to extract configuration file path
 * @param args - Command line arguments (typically process.argv)
 * @returns Configuration file path
 * @throws ConfigurationError when --config argument is missing or invalid
 */
export function parseConfigPath(args: string[]): string {
  const configIndex = args.findIndex(arg => arg === '--config');

  if (configIndex === -1) {
    throw new ConfigurationError(
      'Missing required --config argument. Usage: bun run src/index.ts --config path/to/config.json'
    );
  }

  const configPath = args[configIndex + 1];

  if (!configPath || configPath.startsWith('-')) {
    throw new ConfigurationError(
      'Invalid --config argument. Please provide a valid file path after --config'
    );
  }

  return configPath;
}