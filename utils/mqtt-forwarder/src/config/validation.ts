import type { AppConfig, BindingConfig, BrokerConfig, SourceConfig, TargetConfig, LogLevel } from '../types';
import { ConfigurationError } from './loader';

/**
 * Validates if a value is a non-empty string
 */
function isNonEmptyString(value: unknown): value is string {
  return typeof value === 'string' && value.trim().length > 0;
}

/**
 * Validates if a value is a valid log level
 */
function isValidLogLevel(value: unknown): value is LogLevel {
  return typeof value === 'string' && ['debug', 'info', 'warn', 'error'].includes(value);
}

/**
 * Validates broker configuration
 */
function validateBrokerConfig(broker: unknown, context: string): BrokerConfig {
  if (!broker || typeof broker !== 'object') {
    throw new ConfigurationError(`${context} must be an object`);
  }

  const brokerObj = broker as Record<string, unknown>;

  // Validate required brokerUrl
  if (!isNonEmptyString(brokerObj.brokerUrl)) {
    throw new ConfigurationError(`${context}.brokerUrl is required and must be a non-empty string`);
  }

  // Validate URL format
  try {
    new URL(brokerObj.brokerUrl);
  } catch {
    throw new ConfigurationError(`${context}.brokerUrl must be a valid URL`);
  }

  const validatedBroker: BrokerConfig = {
    brokerUrl: brokerObj.brokerUrl
  };

  // Validate optional username
  if (brokerObj.username !== undefined) {
    if (!isNonEmptyString(brokerObj.username)) {
      throw new ConfigurationError(`${context}.username must be a non-empty string if provided`);
    }
    validatedBroker.username = brokerObj.username;
  }

  // Validate optional password
  if (brokerObj.password !== undefined) {
    if (!isNonEmptyString(brokerObj.password)) {
      throw new ConfigurationError(`${context}.password must be a non-empty string if provided`);
    }
    validatedBroker.password = brokerObj.password;
  }

  return validatedBroker;
}

/**
 * Validates binding configuration
 */
function validateBindingConfig(binding: unknown, index: number): BindingConfig {
  if (!binding || typeof binding !== 'object') {
    throw new ConfigurationError(`bindings[${index}] must be an object`);
  }

  const bindingObj = binding as Record<string, unknown>;
  const context = `bindings[${index}]`;

  // Validate optional name
  let name: string | undefined;
  if (bindingObj.name !== undefined) {
    if (!isNonEmptyString(bindingObj.name)) {
      throw new ConfigurationError(`${context}.name must be a non-empty string if provided`);
    }
    name = bindingObj.name;
  }

  // Validate required source
  if (!bindingObj.source) {
    throw new ConfigurationError(`${context}.source is required`);
  }

  const sourceBroker = validateBrokerConfig(bindingObj.source, `${context}.source`);

  // Validate source topic
  const sourceObj = bindingObj.source as Record<string, unknown>;
  if (!isNonEmptyString(sourceObj.topic)) {
    throw new ConfigurationError(`${context}.source.topic is required and must be a non-empty string`);
  }

  const source: SourceConfig = {
    ...sourceBroker,
    topic: sourceObj.topic
  };

  // Validate required target
  if (!bindingObj.target) {
    throw new ConfigurationError(`${context}.target is required`);
  }

  const targetBroker = validateBrokerConfig(bindingObj.target, `${context}.target`);

  // Validate optional target topic
  const targetObj = bindingObj.target as Record<string, unknown>;
  const target: TargetConfig = {
    ...targetBroker
  };

  if (targetObj.topic !== undefined) {
    if (!isNonEmptyString(targetObj.topic)) {
      throw new ConfigurationError(`${context}.target.topic must be a non-empty string if provided`);
    }
    target.topic = targetObj.topic;
  }

  const validatedBinding: BindingConfig = {
    source,
    target
  };

  if (name) {
    validatedBinding.name = name;
  }

  return validatedBinding;
}

/**
 * Applies default values to configuration
 * @param config - Configuration object to apply defaults to
 * @returns Configuration with defaults applied
 */
function applyDefaults(config: AppConfig): AppConfig {
  return {
    ...config,
    // Set default log level to 'info' if not specified
    logLevel: config.logLevel ?? 'info'
  };
}

/**
 * Validates the complete application configuration
 * @param config - Raw configuration object to validate
 * @returns Validated configuration with defaults applied
 * @throws ConfigurationError when validation fails
 */
export function validateConfig(config: unknown): AppConfig {
  if (!config || typeof config !== 'object') {
    throw new ConfigurationError('Configuration must be an object');
  }

  const configObj = config as Record<string, unknown>;

  // Validate required bindings array
  if (!Array.isArray(configObj.bindings)) {
    throw new ConfigurationError('bindings must be an array');
  }

  if (configObj.bindings.length === 0) {
    throw new ConfigurationError('bindings array cannot be empty');
  }

  // Validate each binding
  const bindings = configObj.bindings.map((binding, index) =>
    validateBindingConfig(binding, index)
  );

  // Validate binding names uniqueness if provided
  const names = bindings
    .map(b => b.name)
    .filter((name): name is string => name !== undefined);

  const uniqueNames = new Set(names);
  if (names.length !== uniqueNames.size) {
    throw new ConfigurationError('Binding names must be unique');
  }

  const validatedConfig: AppConfig = {
    bindings
  };

  // Validate optional logLevel
  if (configObj.logLevel !== undefined) {
    if (!isValidLogLevel(configObj.logLevel)) {
      throw new ConfigurationError('logLevel must be one of: debug, info, warn, error');
    }
    validatedConfig.logLevel = configObj.logLevel;
  }

  // Apply default values
  return applyDefaults(validatedConfig);
}