import type { AppConfig } from '../types';
import { logger } from '../logger';
import { parseConfigPath, readConfigFile } from './loader';
import { validateConfig } from './validation';

/**
 * Loads and validates application configuration from command line arguments
 * @param args - Command line arguments (typically process.argv)
 * @returns Validated configuration with defaults applied
 * @throws ConfigurationError when loading or validation fails
 */
export function loadConfig(args: string[]): AppConfig {
  logger.debug('Loading configuration from command line arguments');

  // Parse configuration file path from command line
  const configPath = parseConfigPath(args);
  logger.info(`Loading configuration from: ${configPath}`);

  // Read and parse configuration file
  const rawConfig = readConfigFile(configPath);

  // Validate configuration and apply defaults
  const validatedConfig = validateConfig(rawConfig);

  logger.info('Configuration loaded and validated successfully');
  logger.debug({ bindings: validatedConfig.bindings.length, logLevel: validatedConfig.logLevel }, 'Configuration details');

  return validatedConfig;
}