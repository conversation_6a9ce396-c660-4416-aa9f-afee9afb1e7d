#!/usr/bin/env bun

import { loadConfig } from './config';
import { BindingManager } from './binding';
import { logger, updateLogLevel } from './logger';

/**
 * MQTT Forwarder Application
 *
 * This is the main entry point for the MQTT forwarder application.
 * It handles command line arguments, loads configuration, initializes
 * the binding manager, and manages the application lifecycle.
 */

/**
 * Display usage information
 */
function showUsage(): void {
  console.log(`
MQTT Forwarder - Lightweight MQTT message broker bridge

Usage:
  bun run src/index.ts --config <config-file>
  bun run start --config <config-file>

Options:
  --config <file>    Path to the configuration JSON file (required)
  --help, -h         Show this help message

Examples:
  bun run src/index.ts --config config.json
  bun run start --config /path/to/config.json

Configuration file should be a JSON file with the following structure:
{
  "logLevel": "info",
  "bindings": [
    {
      "name": "example-binding",
      "source": {
        "brokerUrl": "mqtt://source-broker:1883",
        "username": "user",
        "password": "pass",
        "topic": "source/topic/+"
      },
      "target": {
        "brokerUrl": "mqtt://target-broker:1883",
        "username": "user",
        "password": "pass",
        "topic": "target/\${topic}/forwarded"
      }
    }
  ]
}
`);
}

/**
 * Parse command line arguments
 */
function parseArguments(args: string[]): { configPath?: string; showHelp: boolean } {
  const result = { configPath: undefined as string | undefined, showHelp: false };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (!arg) continue;

    switch (arg) {
      case '--config':
        if (i + 1 < args.length) {
          result.configPath = args[i + 1];
          i++; // Skip next argument as it's the config path
        } else {
          throw new Error('--config requires a file path');
        }
        break;

      case '--help':
      case '-h':
        result.showHelp = true;
        break;

      default:
        // Ignore other arguments (like node executable path, script path)
        if (arg.startsWith('-')) {
          throw new Error(`Unknown option: ${arg}`);
        }
        break;
    }
  }

  return result;
}

/**
 * Setup graceful shutdown handlers
 */
function setupShutdownHandlers(bindingManager: BindingManager): void {
  let isShuttingDown = false;

  const gracefulShutdown = async (signal: string) => {
    if (isShuttingDown) {
      logger.warn(`Received ${signal} during shutdown, forcing exit`);
      process.exit(1);
    }

    isShuttingDown = true;
    logger.info(`Received ${signal}, starting graceful shutdown...`);

    try {
      // Stop all bindings
      await bindingManager.stopAll();
      logger.info('All bindings stopped successfully');

      // Exit gracefully
      logger.info('Shutdown complete');
      process.exit(0);
    } catch (error) {
      logger.error({ error: error instanceof Error ? error.message : String(error) }, 'Error during shutdown');
      process.exit(1);
    }
  };

  // Handle termination signals
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    logger.fatal({ error: error.message, stack: error.stack }, 'Uncaught exception');
    gracefulShutdown('uncaughtException');
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    logger.fatal({ reason, promise }, 'Unhandled promise rejection');
    gracefulShutdown('unhandledRejection');
  });
}

/**
 * Initialize and start the application
 */
async function startApplication(configPath: string): Promise<void> {
  try {
    // Load and validate configuration
    logger.info('MQTT Forwarder starting...');
    const config = loadConfig(['--config', configPath]);

    // Update logger with configured log level
    if (config.logLevel) {
      updateLogLevel(config.logLevel);
      logger.info(`Log level set to: ${config.logLevel}`);
    }

    // Create and configure binding manager
    const bindingManager = new BindingManager();

    // Add all bindings from configuration
    for (const bindingConfig of config.bindings) {
      bindingManager.addBinding(bindingConfig);
      logger.info(`Added binding: ${bindingConfig.name || 'unnamed'}`);
    }

    // Setup graceful shutdown handling
    setupShutdownHandlers(bindingManager);

    // Setup binding manager event handlers for monitoring
    bindingManager.on('binding-started', (bindingName) => {
      logger.info(`Binding started: ${bindingName}`);
    });

    bindingManager.on('binding-stopped', (bindingName) => {
      logger.info(`Binding stopped: ${bindingName}`);
    });

    bindingManager.on('binding-error', (bindingName, error) => {
      logger.error({ error: error.message }, `Binding error: ${bindingName}`);
    });

    bindingManager.on('message-forwarded', (bindingName, sourceTopic, targetTopic, qos) => {
      logger.debug(`Message forwarded [${bindingName}]: ${sourceTopic} -> ${targetTopic} (QoS: ${qos})`);
    });

    bindingManager.on('error', (error) => {
      logger.error({ error: error.message }, 'Binding manager error');
    });

    // Start all bindings
    logger.info(`Starting ${bindingManager.bindingCount} binding(s)...`);
    await bindingManager.startAll();

    logger.info('MQTT Forwarder started successfully');
    logger.info(`Active bindings: ${bindingManager.bindingNames.join(', ')}`);

    // Log statistics periodically (every 30 seconds)
    const statsInterval = setInterval(() => {
      const stats = bindingManager.getStats();
      const summary = stats.map(s =>
        `${s.bindingName}: ${s.messagesForwarded} messages, ${s.errorCount} errors, state: ${s.state}`
      ).join('; ');

      if (summary) {
        logger.info(`Statistics: ${summary}`);
      }
    }, 30000);

    // Clear interval on shutdown
    process.on('SIGINT', () => clearInterval(statsInterval));
    process.on('SIGTERM', () => clearInterval(statsInterval));

  } catch (error) {
    logger.fatal({ error: error instanceof Error ? error.message : String(error) }, 'Failed to start application');
    process.exit(1);
  }
}

/**
 * Main entry point
 */
async function main(): Promise<void> {
  try {
    const args = parseArguments(process.argv.slice(2));

    if (args.showHelp) {
      showUsage();
      return;
    }

    if (!args.configPath) {
      console.error('Error: --config parameter is required\n');
      showUsage();
      process.exit(1);
    }

    await startApplication(args.configPath);

  } catch (error) {
    console.error(`Error: ${error instanceof Error ? error.message : String(error)}\n`);
    showUsage();
    process.exit(1);
  }
}

// Start the application if this file is run directly
if (import.meta.main) {
  main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}