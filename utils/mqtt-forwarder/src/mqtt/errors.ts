import { logger } from '../logger';

/**
 * MQTT-specific error types
 */
export enum MQTTErrorType {
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  PROTOCOL_ERROR = 'PROTOCOL_ERROR',
  SUBSCRIPTION_FAILED = 'SUBSCRIPTION_FAILED',
  PUBLISH_FAILED = 'PUBLISH_FAILED',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN = 'UNKNOWN'
}

/**
 * Custom MQTT error class with enhanced error information
 */
export class MQTTError extends Error {
  public readonly type: MQTTErrorType;
  public readonly brokerUrl?: string;
  public readonly clientId?: string;
  public readonly timestamp: Date;

  constructor(
    type: MQTTErrorType,
    message: string,
    options?: {
      brokerUrl?: string;
      clientId?: string;
      cause?: Error;
    }
  ) {
    super(message);
    this.name = 'MQTTError';
    this.type = type;
    this.brokerUrl = options?.brokerUrl;
    this.clientId = options?.clientId;
    this.timestamp = new Date();

    if (options?.cause) {
      this.cause = options.cause;
    }
  }

  /**
   * Get a detailed error description for logging
   */
  getDetailedDescription(): string {
    const parts = [
      `MQTT Error [${this.type}]: ${this.message}`
    ];

    if (this.brokerUrl) {
      parts.push(`Broker: ${this.brokerUrl}`);
    }

    if (this.clientId) {
      parts.push(`Client: ${this.clientId}`);
    }

    if (this.cause instanceof Error) {
      parts.push(`Caused by: ${this.cause.message}`);
    }

    return parts.join(' | ');
  }
}

/**
 * Categorizes generic errors into MQTT-specific error types
 */
export function categorizeMQTTError(error: Error, context?: { brokerUrl?: string; clientId?: string }): MQTTError {
  const message = error.message.toLowerCase();

  let type: MQTTErrorType;

  // Network and connection errors
  if (message.includes('enotfound') || message.includes('dns')) {
    type = MQTTErrorType.NETWORK_ERROR;
  } else if (message.includes('econnrefused') || message.includes('connection refused')) {
    type = MQTTErrorType.CONNECTION_FAILED;
  } else if (message.includes('timeout') || message.includes('timed out')) {
    type = MQTTErrorType.TIMEOUT;
  } else if (message.includes('authentication') || message.includes('unauthorized') || message.includes('bad user name or password')) {
    type = MQTTErrorType.AUTHENTICATION_FAILED;
  } else if (message.includes('protocol') || message.includes('malformed')) {
    type = MQTTErrorType.PROTOCOL_ERROR;
  } else if (message.includes('subscription') || message.includes('subscribe')) {
    type = MQTTErrorType.SUBSCRIPTION_FAILED;
  } else if (message.includes('publish')) {
    type = MQTTErrorType.PUBLISH_FAILED;
  } else {
    type = MQTTErrorType.UNKNOWN;
  }

  return new MQTTError(type, error.message, {
    brokerUrl: context?.brokerUrl,
    clientId: context?.clientId,
    cause: error
  });
}

/**
 * Determines if an error should trigger reconnection
 */
export function shouldReconnectOnError(error: MQTTError): boolean {
  switch (error.type) {
    case MQTTErrorType.NETWORK_ERROR:
    case MQTTErrorType.CONNECTION_FAILED:
    case MQTTErrorType.TIMEOUT:
      return true;

    case MQTTErrorType.AUTHENTICATION_FAILED:
    case MQTTErrorType.PROTOCOL_ERROR:
      return false;

    case MQTTErrorType.SUBSCRIPTION_FAILED:
    case MQTTErrorType.PUBLISH_FAILED:
    case MQTTErrorType.UNKNOWN:
    default:
      return true; // Conservative approach: try to reconnect unless we know it won't help
  }
}

/**
 * Logs MQTT errors with appropriate level and context
 */
export function logMQTTError(error: MQTTError, context?: string): void {
  const logContext = {
    errorType: error.type,
    brokerUrl: error.brokerUrl,
    clientId: error.clientId,
    timestamp: error.timestamp.toISOString(),
    shouldReconnect: shouldReconnectOnError(error)
  };

  const logMessage = context ? `${context}: ${error.getDetailedDescription()}` : error.getDetailedDescription();

  // Use different log levels based on error severity
  switch (error.type) {
    case MQTTErrorType.AUTHENTICATION_FAILED:
    case MQTTErrorType.PROTOCOL_ERROR:
      logger.error(logContext, logMessage);
      break;

    case MQTTErrorType.NETWORK_ERROR:
    case MQTTErrorType.CONNECTION_FAILED:
    case MQTTErrorType.TIMEOUT:
      logger.warn(logContext, logMessage);
      break;

    default:
      logger.error(logContext, logMessage);
      break;
  }
}