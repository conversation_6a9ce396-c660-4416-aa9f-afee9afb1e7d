import mqtt, { MqttClient, type IClientOptions } from 'mqtt';
import { EventEmitter } from 'events';
import type { BrokerConfig } from '../types';
import { logger } from '../logger';
import { MQTTError, MQTTErrorType, categorizeMQTTError, shouldReconnectOnError, logMQTTError } from './errors';

/**
 * Connection state of the MQTT client
 */
export enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

/**
 * MQTT message structure
 */
export interface MQTTMessage {
  topic: string;
  payload: Buffer;
  qos: 0 | 1 | 2;
  retain: boolean;
}

/**
 * Events emitted by the MQTTClientWrapper
 */
export interface MQTTClientEvents {
  'connected': () => void;
  'disconnected': (error?: Error) => void;
  'error': (error: Error) => void;
  'message': (message: MQTTMessage) => void;
  'subscribed': (topic: string) => void;
  'unsubscribed': (topic: string) => void;
}

/**
 * Reconnection configuration
 */
export interface ReconnectionConfig {
  enabled: boolean;
  initialDelay: number;
  maxDelay: number;
  maxAttempts?: number;
}

/**
 * MQTT client wrapper that provides a consistent interface around MQTT.js
 */
export class MQTTClientWrapper extends EventEmitter {
  private client: MqttClient | null = null;
  private state: ConnectionState = ConnectionState.DISCONNECTED;
  private config: BrokerConfig;
  private clientId: string;

  // Reconnection properties
  private reconnectionConfig: ReconnectionConfig;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private reconnectDelay = 1000;
  private shouldReconnect = true;

  constructor(config: BrokerConfig, clientId?: string, reconnectionConfig?: Partial<ReconnectionConfig>) {
    super();
    this.config = config;
    this.clientId = clientId || `mqtt-forwarder-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Set default reconnection configuration
    this.reconnectionConfig = {
      enabled: true,
      initialDelay: 1000,
      maxDelay: 30000,
      maxAttempts: undefined, // Unlimited by default
      ...reconnectionConfig
    };

    this.reconnectDelay = this.reconnectionConfig.initialDelay;
  }

  /**
   * Get current connection state
   */
  get connectionState(): ConnectionState {
    return this.state;
  }

  /**
   * Check if client is connected
   */
  get isConnected(): boolean {
    return this.state === ConnectionState.CONNECTED;
  }

  /**
   * Build MQTT connection options from broker configuration
   */
  private buildConnectionOptions(): IClientOptions {
    const options: IClientOptions = {
      clientId: this.clientId,
      clean: true,
      connectTimeout: 30000,
      reconnectPeriod: 0, // We handle reconnection ourselves
      keepalive: 60
    };

    // Add authentication if provided
    if (this.config.username) {
      options.username = this.config.username;
    }
    if (this.config.password) {
      options.password = this.config.password;
    }

    return options;
  }

  /**
   * Connect to the MQTT broker
   */
  async connect(): Promise<void> {
    if (this.client && this.isConnected) {
      logger.debug(`MQTT client ${this.clientId} already connected to ${this.config.brokerUrl}`);
      return;
    }

    return new Promise((resolve, reject) => {
      try {
        this.setState(ConnectionState.CONNECTING);
        logger.debug(`Connecting MQTT client ${this.clientId} to ${this.config.brokerUrl}`);

        const options = this.buildConnectionOptions();
        this.client = mqtt.connect(this.config.brokerUrl, options);

        this.setupEventHandlers();

        // Set up connection timeout
        const connectTimeout = setTimeout(() => {
          this.cleanupClient();
          const mqttError = new MQTTError(
            MQTTErrorType.TIMEOUT,
            `Connection timeout to ${this.config.brokerUrl}`,
            { brokerUrl: this.config.brokerUrl, clientId: this.clientId }
          );
          this.setState(ConnectionState.ERROR);
          logMQTTError(mqttError, 'Connection timeout');
          this.emit('error', mqttError);
          reject(mqttError);
        }, 30000);

        this.client.once('connect', () => {
          clearTimeout(connectTimeout);
          this.setState(ConnectionState.CONNECTED);
          logger.info(`MQTT client ${this.clientId} connected to ${this.config.brokerUrl}`);
          this.emit('connected');
          resolve();
        });

        this.client.once('error', (error) => {
          clearTimeout(connectTimeout);
          this.setState(ConnectionState.ERROR);

          const mqttError = categorizeMQTTError(error, {
            brokerUrl: this.config.brokerUrl,
            clientId: this.clientId
          });

          logMQTTError(mqttError, 'Connection failed');
          this.emit('error', mqttError);
          reject(mqttError);
        });

      } catch (error) {
        this.setState(ConnectionState.ERROR);
        const baseError = error instanceof Error ? error : new Error(String(error));
        const mqttError = categorizeMQTTError(baseError, {
          brokerUrl: this.config.brokerUrl,
          clientId: this.clientId
        });

        logMQTTError(mqttError, 'Connection setup failed');
        this.emit('error', mqttError);
        reject(mqttError);
      }
    });
  }

  /**
   * Disconnect from the MQTT broker
   */
  async disconnect(): Promise<void> {
    if (!this.client) {
      logger.debug(`MQTT client ${this.clientId} already disconnected`);
      return;
    }

    return new Promise((resolve) => {
      logger.debug(`Disconnecting MQTT client ${this.clientId}`);

      // Disable reconnection on manual disconnect
      this.shouldReconnect = false;
      this.cancelReconnection();

      if (this.client) {
        this.client.end(false, {}, () => {
          this.cleanupClient();
          this.setState(ConnectionState.DISCONNECTED);
          logger.info(`MQTT client ${this.clientId} disconnected`);
          this.emit('disconnected');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * Enable or disable automatic reconnection
   */
  setReconnectionEnabled(enabled: boolean): void {
    this.shouldReconnect = enabled;
    if (!enabled) {
      this.cancelReconnection();
    }
    logger.debug(`MQTT client ${this.clientId} reconnection ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Cancel any pending reconnection attempt
   */
  private cancelReconnection(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
      logger.debug(`MQTT client ${this.clientId} reconnection cancelled`);
    }
  }

  /**
   * Schedule a reconnection attempt with exponential backoff
   */
  private scheduleReconnection(): void {
    if (!this.shouldReconnect || !this.reconnectionConfig.enabled) {
      return;
    }

    // Check if we've exceeded max attempts
    if (this.reconnectionConfig.maxAttempts && this.reconnectAttempts >= this.reconnectionConfig.maxAttempts) {
      logger.error(`MQTT client ${this.clientId} exceeded maximum reconnection attempts (${this.reconnectionConfig.maxAttempts})`);
      this.setState(ConnectionState.ERROR);
      return;
    }

    this.cancelReconnection();

    logger.info(`MQTT client ${this.clientId} scheduling reconnection attempt ${this.reconnectAttempts + 1} in ${this.reconnectDelay}ms`);

    this.reconnectTimeout = setTimeout(() => {
      this.attemptReconnection();
    }, this.reconnectDelay);

    // Exponential backoff: double the delay, capped at maxDelay
    this.reconnectDelay = Math.min(this.reconnectDelay * 2, this.reconnectionConfig.maxDelay);
  }

  /**
   * Attempt to reconnect to the MQTT broker
   */
  private async attemptReconnection(): Promise<void> {
    if (!this.shouldReconnect || !this.reconnectionConfig.enabled) {
      return;
    }

    this.reconnectAttempts++;
    this.setState(ConnectionState.RECONNECTING);

    logger.info(`MQTT client ${this.clientId} attempting reconnection (attempt ${this.reconnectAttempts})`);

    try {
      await this.connect();

      // Reset reconnection state on successful connection
      this.reconnectAttempts = 0;
      this.reconnectDelay = this.reconnectionConfig.initialDelay;
      logger.info(`MQTT client ${this.clientId} reconnected successfully`);

    } catch (error) {
      logger.warn({ error: (error as Error).message }, `MQTT client ${this.clientId} reconnection attempt ${this.reconnectAttempts} failed`);

      // Schedule next attempt if we should continue
      this.scheduleReconnection();
    }
  }

  /**
   * Reset reconnection state (useful when connection is restored externally)
   */
  private resetReconnectionState(): void {
    this.reconnectAttempts = 0;
    this.reconnectDelay = this.reconnectionConfig.initialDelay;
    this.cancelReconnection();
  }

  /**
   * Subscribe to a topic
   */
  async subscribe(topic: string, qos: 0 | 1 | 2 = 0): Promise<void> {
    if (!this.client || !this.isConnected) {
      throw new Error('Client must be connected before subscribing');
    }

    return new Promise((resolve, reject) => {
      logger.debug(`MQTT client ${this.clientId} subscribing to topic: ${topic} (QoS ${qos})`);

      this.client!.subscribe(topic, { qos }, (error) => {
        if (error) {
          const mqttError = new MQTTError(
            MQTTErrorType.SUBSCRIPTION_FAILED,
            `Failed to subscribe to topic: ${topic}`,
            { brokerUrl: this.config.brokerUrl, clientId: this.clientId, cause: error }
          );

          logMQTTError(mqttError, 'Subscription failed');
          reject(mqttError);
          return;
        }

        logger.info(`MQTT client ${this.clientId} subscribed to topic: ${topic}`);
        this.emit('subscribed', topic);
        resolve();
      });
    });
  }

  /**
   * Unsubscribe from a topic
   */
  async unsubscribe(topic: string): Promise<void> {
    if (!this.client || !this.isConnected) {
      throw new Error('Client must be connected before unsubscribing');
    }

    return new Promise((resolve, reject) => {
      logger.debug(`MQTT client ${this.clientId} unsubscribing from topic: ${topic}`);

      this.client!.unsubscribe(topic, (error) => {
        if (error) {
          const mqttError = new MQTTError(
            MQTTErrorType.SUBSCRIPTION_FAILED,
            `Failed to unsubscribe from topic: ${topic}`,
            { brokerUrl: this.config.brokerUrl, clientId: this.clientId, cause: error }
          );

          logMQTTError(mqttError, 'Unsubscription failed');
          reject(mqttError);
          return;
        }

        logger.info(`MQTT client ${this.clientId} unsubscribed from topic: ${topic}`);
        this.emit('unsubscribed', topic);
        resolve();
      });
    });
  }

  /**
   * Publish a message to a topic
   */
  async publish(topic: string, payload: Buffer | string, qos: 0 | 1 | 2 = 0, retain: boolean = false): Promise<void> {
    if (!this.client || !this.isConnected) {
      throw new Error('Client must be connected before publishing');
    }

    return new Promise((resolve, reject) => {
      logger.debug(`MQTT client ${this.clientId} publishing to topic: ${topic} (QoS ${qos}, retain: ${retain})`);

      this.client!.publish(topic, payload, { qos, retain }, (error) => {
        if (error) {
          const mqttError = new MQTTError(
            MQTTErrorType.PUBLISH_FAILED,
            `Failed to publish to topic: ${topic}`,
            { brokerUrl: this.config.brokerUrl, clientId: this.clientId, cause: error }
          );

          logMQTTError(mqttError, 'Publish failed');
          reject(mqttError);
          return;
        }

        logger.debug(`MQTT client ${this.clientId} published to topic: ${topic}`);
        resolve();
      });
    });
  }

  /**
   * Setup event handlers for the MQTT client
   */
  private setupEventHandlers(): void {
    if (!this.client) return;

    this.client.on('close', () => {
      logger.debug(`MQTT client ${this.clientId} connection closed`);
      if (this.state !== ConnectionState.DISCONNECTED) {
        this.setState(ConnectionState.DISCONNECTED);
        this.emit('disconnected');

        // Schedule reconnection if enabled and this wasn't a manual disconnect
        if (this.shouldReconnect && this.reconnectionConfig.enabled) {
          this.scheduleReconnection();
        }
      }
    });

    this.client.on('offline', () => {
      logger.debug(`MQTT client ${this.clientId} went offline`);
      if (this.state !== ConnectionState.DISCONNECTED && this.state !== ConnectionState.RECONNECTING) {
        this.setState(ConnectionState.DISCONNECTED);
        this.emit('disconnected');

        // Schedule reconnection if enabled
        if (this.shouldReconnect && this.reconnectionConfig.enabled) {
          this.scheduleReconnection();
        }
      }
    });

    this.client.on('connect', () => {
      // Reset reconnection state on successful connection
      this.resetReconnectionState();
    });

    this.client.on('message', (topic, payload, packet) => {
      logger.debug(`MQTT client ${this.clientId} received message on topic: ${topic}`);

      const message: MQTTMessage = {
        topic,
        payload,
        qos: packet.qos as (0 | 1 | 2),
        retain: packet.retain || false
      };

      this.emit('message', message);
    });

    this.client.on('error', (error) => {
      const mqttError = categorizeMQTTError(error, {
        brokerUrl: this.config.brokerUrl,
        clientId: this.clientId
      });

      logMQTTError(mqttError, 'MQTT client error');
      this.setState(ConnectionState.ERROR);
      this.emit('error', mqttError);

      // Don't schedule reconnection on error, let close/offline events handle it
    });
  }

  /**
   * Update connection state and log changes
   */
  private setState(newState: ConnectionState): void {
    if (this.state !== newState) {
      const oldState = this.state;
      this.state = newState;
      logger.debug(`MQTT client ${this.clientId} state changed: ${oldState} -> ${newState}`);
    }
  }

  /**
   * Clean up client resources
   */
  private cleanupClient(): void {
    if (this.client) {
      this.client.removeAllListeners();
      this.client = null;
    }
    this.cancelReconnection();
  }

  /**
   * Override EventEmitter methods to provide type safety
   */
  override on<K extends keyof MQTTClientEvents>(event: K, listener: MQTTClientEvents[K]): this {
    return super.on(event, listener);
  }

  override emit<K extends keyof MQTTClientEvents>(event: K, ...args: Parameters<MQTTClientEvents[K]>): boolean {
    return super.emit(event, ...args);
  }
}