# MQTT Forwarder

A lightweight, high-performance MQTT message broker bridge that forwards MQTT messages from source brokers to target brokers with configurable topic mappings and transformation capabilities.

## Features

- 🔄 **Multi-Binding Support**: Configure multiple independent source→target broker pairs
- 🎯 **Wildcard Topic Subscriptions**: Support for MQTT wildcards (`+` and `#`)
- 🔧 **Dynamic Topic Transformation**: Transform target topics using `${topic}` placeholders
- 🔌 **Automatic Reconnection**: Exponential backoff reconnection with configurable limits
- 📊 **Message Preservation**: Exact payload and QoS forwarding
- 📝 **Configurable Logging**: Multiple log levels using Pino logger
- 🛠️ **JSON Configuration**: Simple, validated configuration loading
- 📈 **Statistics Tracking**: Built-in message counting and error tracking
- 🛡️ **Graceful Shutdown**: Clean resource cleanup on termination signals
- 🔐 **Authentication Support**: Username/password authentication for brokers

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd mqtt-forwarder

# Install dependencies
bun install

# Run tests
bun test

# Type check
bun run typecheck
```

## Quick Start

1. **Create a configuration file** (see [config.example.json](config.example.json) for reference):

```json
{
  "logLevel": "info",
  "bindings": [
    {
      "name": "my-forwarder",
      "source": {
        "brokerUrl": "mqtt://source-broker:1883",
        "topic": "sensors/+/data"
      },
      "target": {
        "brokerUrl": "mqtt://target-broker:1883",
        "topic": "forwarded/${topic}"
      }
    }
  ]
}
```

2. **Run the forwarder**:

```bash
# Using bun directly
bun run src/index.ts --config config.json

# Using npm scripts
bun run start --config config.json

# Show help
bun run src/index.ts --help
```

## Usage

### Basic Commands

```bash
# Start with configuration file
bun run src/index.ts --config config.json

# Start with help information
bun run src/index.ts --help

# Run in development with auto-restart
bun run start --config config.json
```

## Configuration

The MQTT forwarder uses a JSON configuration file with the following structure:

### Configuration Schema

```typescript
{
  "logLevel": "debug" | "info" | "warn" | "error",  // Optional, default: "info"
  "bindings": [                                     // Required, array of bindings
    {
      "name": "string",                            // Optional, binding identifier
      "source": {
        "brokerUrl": "string",                     // Required, MQTT broker URL
        "username": "string",                      // Optional, authentication
        "password": "string",                      // Optional, authentication
        "topic": "string"                          // Required, topic to subscribe to
      },
      "target": {
        "brokerUrl": "string",                     // Required, MQTT broker URL
        "username": "string",                      // Optional, authentication
        "password": "string",                      // Optional, authentication
        "topic": "string"                          // Optional, topic transformation template
      }
    }
  ]
}
```

### Configuration Options

#### Application Level
- **`logLevel`** (optional): Controls logging verbosity
  - `"debug"`: Detailed connection events, message flow, transformation details
  - `"info"`: Connection status, binding activation, important events (default)
  - `"warn"`: Warnings and important issues
  - `"error"`: Only errors and critical issues

#### Binding Level
- **`name`** (optional): Human-readable identifier for the binding. If not provided, auto-generated from broker URLs
- **`source`** (required): Source broker configuration
- **`target`** (required): Target broker configuration

#### Broker Configuration
- **`brokerUrl`** (required): MQTT broker connection URL. Supported schemes:
  - `mqtt://` - Standard MQTT
  - `mqtts://` - MQTT over TLS
  - `ws://` - MQTT over WebSocket
  - `wss://` - MQTT over Secure WebSocket
- **`username`** (optional): Authentication username
- **`password`** (optional): Authentication password
- **`topic`** (required for source, optional for target): See topic configuration below

#### Topic Configuration

**Source Topics:**
- Support MQTT wildcards:
  - `+` - Single level wildcard (matches one topic level)
  - `#` - Multi-level wildcard (matches multiple topic levels)
- Examples: `sensors/+/data`, `building/#`, `device/+/sensor/+/temperature`

**Target Topics:**
- Optional transformation template
- Use `${topic}` placeholder to insert the original source topic
- If omitted, uses source topic unchanged

### Topic Transformation Examples

| Source Topic | Target Template | Result |
|--------------|-----------------|---------|
| `a/b/c` | (not provided) | `a/b/c` |
| `a/b/c` | `x/y/${topic}` | `x/y/a/b/c` |
| `a/b/c` | `x/y/${topic}/z` | `x/y/a/b/c/z` |
| `a/b/c` | `x/y/z` | `x/y/z` |

## Configuration Examples

### 1. Simple Message Relay

```json
{
  "logLevel": "info",
  "bindings": [
    {
      "name": "simple-relay",
      "source": {
        "brokerUrl": "mqtt://source.example.com:1883",
        "topic": "sensors/temperature"
      },
      "target": {
        "brokerUrl": "mqtt://target.example.com:1883"
      }
    }
  ]
}
```

### 2. IoT Data Collection with Authentication

```json
{
  "logLevel": "info",
  "bindings": [
    {
      "name": "iot-data-collector",
      "source": {
        "brokerUrl": "mqtt://iot-devices.local:1883",
        "username": "device_reader",
        "password": "secure_pass_123",
        "topic": "devices/+/sensor/+"
      },
      "target": {
        "brokerUrl": "mqtts://cloud-analytics.example.com:8883",
        "username": "analytics_writer",
        "password": "cloud_token_456",
        "topic": "analytics/iot/${topic}/processed"
      }
    }
  ]
}
```

### 3. Multi-Building HVAC System

```json
{
  "logLevel": "debug",
  "bindings": [
    {
      "name": "building-a-hvac",
      "source": {
        "brokerUrl": "mqtt://building-a-sensors:1883",
        "topic": "building/floor/+/room/+/temperature"
      },
      "target": {
        "brokerUrl": "mqtt://central-hvac:1883",
        "topic": "hvac/building-a/${topic}"
      }
    },
    {
      "name": "building-b-hvac",
      "source": {
        "brokerUrl": "mqtt://building-b-sensors:1883",
        "topic": "building/floor/+/room/+/temperature"
      },
      "target": {
        "brokerUrl": "mqtt://central-hvac:1883",
        "topic": "hvac/building-b/${topic}"
      }
    }
  ]
}
```

### 4. Production Line Monitoring

```json
{
  "logLevel": "info",
  "bindings": [
    {
      "name": "production-monitor",
      "source": {
        "brokerUrl": "mqtt://factory-floor:1883",
        "username": "monitor",
        "password": "factory_2024",
        "topic": "production/line/+/machine/#"
      },
      "target": {
        "brokerUrl": "ws://dashboard.factory.com:8080",
        "username": "dashboard",
        "password": "viz_token",
        "topic": "dashboard/production/${topic}/live"
      }
    }
  ]
}
```

## Command Line Interface

```
MQTT Forwarder - Lightweight MQTT message broker bridge

Usage:
  bun run src/index.ts --config <config-file>
  bun run start --config <config-file>

Options:
  --config <file>    Path to the configuration JSON file (required)
  --help, -h         Show this help message

Examples:
  bun run src/index.ts --config config.json
  bun run start --config /path/to/production-config.json
```

## Monitoring and Logging

The forwarder provides comprehensive logging and monitoring capabilities:

### Log Levels

- **`debug`**: Detailed connection events, message flow, transformation details
- **`info`**: Connection status, binding activation, message forwarding events
- **`warn`**: Connection issues, reconnection attempts
- **`error`**: Connection failures, processing errors, configuration issues

### Statistics

The application tracks and logs statistics every 30 seconds:

```
[INFO] Statistics: sensor-data-collector: 1,247 messages, 2 errors, state: running; alert-forwarder: 89 messages, 0 errors, state: running
```

### Log Output Examples

```
[INFO] MQTT Forwarder starting...
[INFO] Log level set to: info
[INFO] Added binding: sensor-data-collector
[INFO] Starting 1 binding(s)...
[INFO] Binding started: sensor-data-collector
[INFO] MQTT Forwarder started successfully
[DEBUG] [sensor-data-collector] Message forwarded: sensors/temp/data -> warehouse/sensors/temp/data (QoS: 0)
```

## Production Deployment

### Environment Setup

1. **Create production configuration**:
```bash
cp config.example.json production-config.json
# Edit production-config.json with your broker settings
```

2. **Run with production settings**:
```bash
# Set log level to reduce verbosity
bun run src/index.ts --config production-config.json
```

### Best Practices

- **Security**: Use authentication credentials for all broker connections
- **TLS**: Use `mqtts://` URLs for encrypted connections in production
- **Monitoring**: Monitor log output for connection issues and errors
- **Resource Management**: The forwarder automatically manages connections and handles reconnections
- **Graceful Shutdown**: The application handles SIGINT/SIGTERM signals for clean shutdown

### Error Handling

The forwarder is designed to be resilient:

- **Automatic Reconnection**: Exponential backoff (1s to 30s) with unlimited retry attempts
- **Individual Binding Isolation**: Errors in one binding don't affect others
- **Message Processing**: Malformed messages are logged but don't crash the application
- **Configuration Validation**: Invalid configurations are caught at startup

### Performance Considerations

- **Memory Usage**: Low memory footprint, scales with number of concurrent connections
- **Message Throughput**: Handles high-frequency message forwarding efficiently
- **Connection Pooling**: Each binding maintains separate source and target connections
- **QoS Preservation**: Maintains original message QoS levels during forwarding

## Troubleshooting

### Common Issues

1. **Connection Failures**:
   ```
   [ERROR] Source client error: connect ECONNREFUSED 127.0.0.1:1883
   ```
   - Verify broker URLs are correct and accessible
   - Check authentication credentials
   - Ensure network connectivity

2. **Authentication Errors**:
   ```
   [ERROR] Connection failed: Connection refused: Not authorized
   ```
   - Verify username and password
   - Check broker authentication configuration

3. **Topic Subscription Issues**:
   ```
   [ERROR] Failed to subscribe to topic: sensors/+/data
   ```
   - Verify topic pattern is valid
   - Check broker supports wildcard subscriptions
   - Verify client has subscription permissions

### Debug Mode

Enable debug logging for detailed troubleshooting:

```json
{
  "logLevel": "debug",
  "bindings": [...]
}
```

This provides detailed information about:
- Connection establishment and termination
- Message flow and topic transformations
- Reconnection attempts and timing
- Internal state changes

## License

[Insert your license information here]

## Contributing

[Insert contribution guidelines here]

## Support

For issues and questions:
- Check the troubleshooting section above
- Review log output with debug level enabled
- Verify configuration against the schema
- Test connectivity to MQTT brokers independently
