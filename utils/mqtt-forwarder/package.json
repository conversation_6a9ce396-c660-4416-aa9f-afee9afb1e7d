{"name": "mqtt-forwarder", "version": "1.0.0-alpha.1", "module": "src/index.ts", "type": "module", "private": true, "scripts": {"start": "bun run src/index.ts", "test": "bun test", "test:watch": "bun test --watch", "typecheck": "bunx tsc --noEmit"}, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"mqtt": "^5.14.1", "pino": "^9.11.0", "pino-pretty": "^13.1.1"}}