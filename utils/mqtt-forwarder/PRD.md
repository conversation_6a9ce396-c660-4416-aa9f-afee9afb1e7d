# MQTT Topic Forwarder - Product Requirements Document (PRD)

## 1. Overview

### 1.1. Product Description

The MQTT Topic Forwarder is a lightweight message broker bridge that forwards MQTT messages from source brokers to target brokers based on configurable topic mappings. It enables seamless message routing between different MQTT broker instances while supporting topic transformation rules.

### 1.2. Key Features

- Multi-binding support (multiple source→target configurations)
- Wildcard topic subscription support
- Dynamic topic transformation with placeholder substitution
- Automatic reconnection handling
- Exact message payload and QoS forwarding
- Configurable logging levels
- JSON-based configuration

## 2. Technical Specifications

### 2.1. Technology Stack

- **Runtime**: Bun
- **Language**: TypeScript
- **MQTT Client**: MQTT.js library
- **Logging**: Pino logging library

### 2.2. Configuration File Format

The application will read a JSON configuration file with the following structure:

```json
{
  "bindings": [
    {
      "name": "optional-binding-name",
      "source": {
        "brokerUrl": "mqtt://source-broker:1883",
        "username": "optional-username",
        "password": "optional-password",
        "topic": "source/topic/pattern/#"
      },
      "target": {
        "brokerUrl": "mqtt://target-broker:1883",
        "username": "optional-username",
        "password": "optional-password",
        "topic": "transformed/${topic}/path"
      }
    }
  ],
  "logLevel": "info"
}
```

### 2.3. Core Requirements

#### 2.3.1. Message Forwarding Logic

- **Payload Preservation**: Forward messages with exact payload content (no modification)
- **QoS Preservation**: Maintain original Quality of Service level
- **One-Way Forwarding**: Only forward from source to target (no bidirectional forwarding)
- **Topic Transformation**: Support `${topic}` placeholder replacement in target topics

#### 2.3.2. Connection Management

- **Automatic Reconnection**: Implement exponential backoff for connection failures
- **Error Handling**: Gracefully handle network errors, authentication failures, and malformed messages
- **Clean Disconnection**: Properly close connections on application shutdown

#### 2.3.3. Multiple Binding Support

- **Independent Bindings**: Each binding operates independently with separate connections
- **Concurrent Operation**: Handle multiple bindings simultaneously without blocking
- **Resource Management**: Efficiently manage connections and subscriptions

## 3. Functional Requirements

### 3.1. Configuration Loading

- Load configuration from JSON file specified via command line argument
- Validate configuration structure and required fields
- Set default log level to "info" if not specified

### 3.2. MQTT Client Management

#### 3.2.1. Source Client

- Establish connection to source broker using provided credentials
- Subscribe to specified topic (supporting MQTT wildcards: +, #)
- Handle incoming messages and forward to target client

#### 3.2.2. Target Client

- Maintain persistent connection to target broker
- Publish messages with transformed topics while preserving payload and QoS

### 3.3. Topic Transformation

The system must support the following topic transformation patterns:

| Source Topic | Target Template  | Result Target Topic |
| ------------ | ---------------- | ------------------- |
| `a/b/c`      | (not provided)   | `a/b/c`             |
| `a/b/c`      | `x/y/${topic}`   | `x/y/a/b/c`         |
| `a/b/c`      | `x/y/${topic}/z` | `x/y/a/b/c/z`       |
| `a/b/c`      | `x/y/z`          | `x/y/z`             |

### 3.4. Error Handling & Recovery

#### 3.4.1. Connection Errors

- Detect connection failures for both source and target brokers
- Implement automatic reconnection with increasing delays (exponential backoff)
- Log connection status changes appropriately

#### 3.4.2. Message Processing Errors

- Handle malformed messages gracefully without crashing
- Log errors but continue processing other messages
- Do not attempt to modify or fix message content

### 3.5. Logging Requirements

#### 3.5.1. Log Levels

- **debug**: Detailed connection events, message flow, transformation details
- **info**: Connection status, binding activation, important events
- **error**: Connection failures, processing errors, configuration issues

#### 3.5.2. Log Content

- Include binding name in all log messages for traceability
- Log message forwarding events (source topic → target topic)
- Record connection establishment and termination events

## 4. Non-Functional Requirements

### 4.1. Performance

- Handle high message throughput efficiently
- Minimal latency in message forwarding
- Efficient memory usage for multiple concurrent bindings

### 4.2. Reliability

- 99.9% uptime for forwarding operations
- Automatic recovery from network interruptions
- No message loss during brief disconnections (depending on QoS)

### 4.3. Maintainability

- Clean, modular TypeScript code
- Comprehensive error handling
- Extensible architecture for future enhancements

## 5. Implementation Details

### 5.1. Project Structure

```
mqtt-forwarder/
├── src/
│   ├── index.ts          # Main application entry point
│   ├── config/           # Configuration loading and validation
│   ├── mqtt/             # MQTT client management
│   ├── binding/          # Binding management logic
│   └── logger.ts         # Logger configuration
├── config.json           # Example configuration
├── package.json
└── tsconfig.json
```

### 5.2. Key Components

#### 5.2.1. Configuration Loader

- Validate JSON structure against TypeScript types
- Set default values for optional fields
- Handle file reading errors

#### 5.2.2. Binding Manager

- Create and manage individual bindings
- Handle binding-specific connections
- Coordinate source and target clients

#### 5.2.3. MQTT Client Wrapper

- Abstract MQTT.js connection management
- Implement reconnection logic
- Provide consistent error handling

### 5.3. Core Algorithms

#### 5.3.1. Topic Transformation

```typescript
function transformTopic(sourceTopic: string, targetTemplate?: string): string {
  if (!targetTemplate) return sourceTopic;
  return targetTemplate.replace("${topic}", sourceTopic);
}
```

#### 5.3.2. Reconnection Logic

```typescript
function setupReconnection(client: mqtt.Client, maxDelay: number = 30000) {
  let reconnectDelay = 1000;

  client.on("close", () => {
    setTimeout(() => {
      client.reconnect();
      reconnectDelay = Math.min(reconnectDelay * 2, maxDelay);
    }, reconnectDelay);
  });
}
```

## 6. Deployment & Operation

### 6.1. Startup Command

```bash
bun run src/index.ts --config path/to/config.json
```

### 6.2. Environment Considerations

- Run as a long-lived process (daemon)
- Suitable for containerized deployment (Docker)
- Minimal system resource requirements

### 6.3. Monitoring

- Health checks via logging output
- Connection status monitoring
- Message throughput metrics (via debug logging)

## 7. Acceptance Criteria

### 7.1. Must Have

- ✅ Successfully forwards messages between brokers with correct topic transformation
- ✅ Handles multiple simultaneous bindings independently
- ✅ Automatically reconnects after connection failures
- ✅ Preserves message payload and QoS exactly
- ✅ Supports MQTT wildcard topics in source subscriptions
- ✅ Provides appropriate logging at configured levels

### 7.2. Should Have

- ✅ Clean shutdown on SIGINT/SIGTERM signals
- ✅ Validation of configuration file structure
- ✅ Meaningful error messages for common failure scenarios

### 7.3. Could Have

- ⚠️ Metrics collection for monitoring purposes
- ⚠️ Configuration hot-reload capability
- ⚠️ Health check HTTP endpoint

## 8. Testing Requirements

### 8.1. Unit Tests

- Topic transformation logic
- Configuration validation
- Connection parameter building
