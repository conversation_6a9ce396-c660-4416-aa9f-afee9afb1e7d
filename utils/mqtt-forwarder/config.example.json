{"logLevel": "info", "bindings": [{"name": "sensor-data-collector", "source": {"brokerUrl": "mqtt://iot-sensors.example.com:1883", "username": "sensor_reader", "password": "sensor_pass_123", "topic": "sensors/+/data"}, "target": {"brokerUrl": "mqtt://data-warehouse.example.com:1883", "username": "warehouse_writer", "password": "warehouse_pass_456", "topic": "warehouse/${topic}/processed"}}, {"name": "alert-forwarder", "source": {"brokerUrl": "mqtts://secure-alerts.example.com:8883", "username": "alert_monitor", "password": "alert_secret_789", "topic": "alerts/#"}, "target": {"brokerUrl": "ws://notification-service.example.com:8080", "username": "notifier", "password": "notify_key_101", "topic": "notifications/alerts/${topic}"}}, {"name": "device-status-bridge", "source": {"brokerUrl": "mqtt://device-network.local:1883", "topic": "devices/+/status"}, "target": {"brokerUrl": "mqtt://central-monitoring.local:1883", "topic": "monitoring/status/${topic}"}}, {"name": "temperature-aggregator", "source": {"brokerUrl": "mqtt://building-sensors:1883", "username": "temp_reader", "password": "temp_pass", "topic": "building/+/floor/+/room/+/temperature"}, "target": {"brokerUrl": "mqtt://hvac-controller:1883", "username": "hvac_writer", "password": "hvac_pass", "topic": "hvac/input/${topic}/celsius"}}, {"name": "simple-relay", "source": {"brokerUrl": "mqtt://source-broker:1883", "topic": "source/topic"}, "target": {"brokerUrl": "mqtt://target-broker:1883"}}, {"name": "wildcard-forwarder", "source": {"brokerUrl": "mqtt://production-data:1883", "username": "prod_user", "password": "prod_pass", "topic": "production/line/+/machine/#"}, "target": {"brokerUrl": "mqtt://analytics-platform:1883", "username": "analytics_user", "password": "analytics_pass", "topic": "analytics/manufacturing/${topic}/raw"}}]}