import { describe, test, expect, beforeEach, afterEach, mock } from 'bun:test';
import { MQTTClientWrapper, ConnectionState } from '../src/mqtt/client';
import type { BrokerConfig } from '../src/types';

describe('MQTT Client', () => {
  let clientWrapper: MQTTClientWrapper;

  const defaultBrokerConfig: BrokerConfig = {
    brokerUrl: 'mqtt://test-broker:1883'
  };

  beforeEach(() => {
    // Create a fresh client for each test
    clientWrapper = new MQTTClientWrapper(defaultBrokerConfig);
  });

  afterEach(async () => {
    // Clean up after each test
    if (clientWrapper && clientWrapper.isConnected) {
      await clientWrapper.disconnect();
    }
  });

  describe('Constructor and Initial State', () => {
    test('should initialize with correct default state', () => {
      expect(clientWrapper.connectionState).toBe(ConnectionState.DISCONNECTED);
      expect(clientWrapper.isConnected).toBe(false);
    });

    test('should generate unique client ID when not provided', () => {
      const client1 = new MQTTClientWrapper(defaultBrokerConfig);
      const client2 = new MQTTClientWrapper(defaultBrokerConfig);

      // Access private clientId through reflection for testing
      const client1Id = (client1 as any).clientId;
      const client2Id = (client2 as any).clientId;

      expect(client1Id).toBeDefined();
      expect(client2Id).toBeDefined();
      expect(client1Id).not.toBe(client2Id);
      expect(client1Id).toMatch(/^mqtt-forwarder-\d+-[a-z0-9]+$/);
    });

    test('should use provided client ID when specified', () => {
      const customClientId = 'custom-test-client';
      const client = new MQTTClientWrapper(defaultBrokerConfig, customClientId);

      const actualClientId = (client as any).clientId;
      expect(actualClientId).toBe(customClientId);
    });

    test('should apply custom reconnection configuration', () => {
      const customReconnectionConfig = {
        enabled: false,
        initialDelay: 2000,
        maxDelay: 60000,
        maxAttempts: 5
      };

      const client = new MQTTClientWrapper(
        defaultBrokerConfig,
        'test-client',
        customReconnectionConfig
      );

      const actualConfig = (client as any).reconnectionConfig;
      expect(actualConfig.enabled).toBe(false);
      expect(actualConfig.initialDelay).toBe(2000);
      expect(actualConfig.maxDelay).toBe(60000);
      expect(actualConfig.maxAttempts).toBe(5);
    });
  });

  describe('Connection Options Building', () => {
    test('should build basic connection options', () => {
      const client = new MQTTClientWrapper(defaultBrokerConfig, 'test-client');

      // Access private method for testing
      const options = (client as any).buildConnectionOptions();

      expect(options.clientId).toBe('test-client');
      expect(options.clean).toBe(true);
      expect(options.connectTimeout).toBe(30000);
      expect(options.reconnectPeriod).toBe(0);
      expect(options.keepalive).toBe(60);
      expect(options.username).toBeUndefined();
      expect(options.password).toBeUndefined();
    });

    test('should include authentication when provided', () => {
      const configWithAuth: BrokerConfig = {
        brokerUrl: 'mqtt://secure-broker:1883',
        username: 'testuser',
        password: 'testpass'
      };

      const client = new MQTTClientWrapper(configWithAuth, 'auth-test-client');
      const options = (client as any).buildConnectionOptions();

      expect(options.username).toBe('testuser');
      expect(options.password).toBe('testpass');
    });

    test('should include only username when password is not provided', () => {
      const configWithUsername: BrokerConfig = {
        brokerUrl: 'mqtt://broker:1883',
        username: 'testuser'
      };

      const client = new MQTTClientWrapper(configWithUsername, 'username-test-client');
      const options = (client as any).buildConnectionOptions();

      expect(options.username).toBe('testuser');
      expect(options.password).toBeUndefined();
    });

    test('should not include username when only password is provided', () => {
      const configWithPassword: BrokerConfig = {
        brokerUrl: 'mqtt://broker:1883',
        password: 'testpass'
      };

      const client = new MQTTClientWrapper(configWithPassword, 'password-test-client');
      const options = (client as any).buildConnectionOptions();

      expect(options.username).toBeUndefined();
      expect(options.password).toBe('testpass');
    });
  });

  describe('Reconnection Configuration', () => {
    test('should enable/disable reconnection', () => {
      expect((clientWrapper as any).shouldReconnect).toBe(true);

      clientWrapper.setReconnectionEnabled(false);
      expect((clientWrapper as any).shouldReconnect).toBe(false);

      clientWrapper.setReconnectionEnabled(true);
      expect((clientWrapper as any).shouldReconnect).toBe(true);
    });

    test('should cancel pending reconnection when disabled', () => {
      // Set up a mock timeout
      const mockTimeout = { id: 'mock-timeout' };
      (clientWrapper as any).reconnectTimeout = mockTimeout;

      clientWrapper.setReconnectionEnabled(false);

      // Should clear the timeout
      expect((clientWrapper as any).reconnectTimeout).toBeNull();
    });

    test('should apply default reconnection configuration', () => {
      const config = (clientWrapper as any).reconnectionConfig;

      expect(config.enabled).toBe(true);
      expect(config.initialDelay).toBe(1000);
      expect(config.maxDelay).toBe(30000);
      expect(config.maxAttempts).toBeUndefined();
    });
  });

  describe('State Management', () => {
    test('should transition through connection states', () => {
      const states: ConnectionState[] = [];

      // Mock the setState method to capture state changes
      const originalSetState = (clientWrapper as any).setState;
      (clientWrapper as any).setState = (newState: ConnectionState) => {
        states.push(newState);
        originalSetState.call(clientWrapper, newState);
      };

      // Start with disconnected
      expect(clientWrapper.connectionState).toBe(ConnectionState.DISCONNECTED);

      // Simulate state transitions
      (clientWrapper as any).setState(ConnectionState.CONNECTING);
      expect(clientWrapper.connectionState).toBe(ConnectionState.CONNECTING);
      expect(clientWrapper.isConnected).toBe(false);

      (clientWrapper as any).setState(ConnectionState.CONNECTED);
      expect(clientWrapper.connectionState).toBe(ConnectionState.CONNECTED);
      expect(clientWrapper.isConnected).toBe(true);

      (clientWrapper as any).setState(ConnectionState.ERROR);
      expect(clientWrapper.connectionState).toBe(ConnectionState.ERROR);
      expect(clientWrapper.isConnected).toBe(false);
    });

    test('should not change state if already in target state', () => {
      let stateChangeCount = 0;

      // Mock the setState method to count actual changes
      const originalSetState = (clientWrapper as any).setState;
      (clientWrapper as any).setState = (newState: ConnectionState) => {
        const oldState = clientWrapper.connectionState;
        originalSetState.call(clientWrapper, newState);
        if (oldState !== newState) {
          stateChangeCount++;
        }
      };

      // Should start in DISCONNECTED
      expect(clientWrapper.connectionState).toBe(ConnectionState.DISCONNECTED);

      // Setting to same state should not increment counter
      (clientWrapper as any).setState(ConnectionState.DISCONNECTED);
      expect(stateChangeCount).toBe(0);

      // Setting to different state should increment counter
      (clientWrapper as any).setState(ConnectionState.CONNECTING);
      expect(stateChangeCount).toBe(1);

      // Setting to same state again should not increment counter
      (clientWrapper as any).setState(ConnectionState.CONNECTING);
      expect(stateChangeCount).toBe(1);
    });
  });

  describe('Configuration Validation', () => {
    test('should accept valid broker URLs', () => {
      const validUrls = [
        'mqtt://localhost:1883',
        'mqtts://secure.broker.com:8883',
        'ws://websocket.broker.com:8080',
        'wss://secure-websocket.broker.com:8443',
        'tcp://broker.com:1883'
      ];

      for (const brokerUrl of validUrls) {
        const config: BrokerConfig = { brokerUrl };
        const client = new MQTTClientWrapper(config);

        expect((client as any).config.brokerUrl).toBe(brokerUrl);
      }
    });

    test('should handle authentication configurations', () => {
      const authConfigs = [
        { brokerUrl: 'mqtt://test:1883', username: 'user1' },
        { brokerUrl: 'mqtt://test:1883', password: 'pass1' },
        { brokerUrl: 'mqtt://test:1883', username: 'user1', password: 'pass1' },
        { brokerUrl: 'mqtt://test:1883', username: '', password: '' },
      ];

      for (const config of authConfigs) {
        const client = new MQTTClientWrapper(config);
        const actualConfig = (client as any).config;

        expect(actualConfig.brokerUrl).toBe(config.brokerUrl);
        expect(actualConfig.username).toBe(config.username);
        expect(actualConfig.password).toBe(config.password);
      }
    });
  });

  describe('Event Handling Setup', () => {
    test('should have proper event emitter interface', () => {
      // Test that the client can register event listeners
      const connectHandler = mock(() => {});
      const disconnectHandler = mock(() => {});
      const errorHandler = mock(() => {});
      const messageHandler = mock(() => {});

      clientWrapper.on('connected', connectHandler);
      clientWrapper.on('disconnected', disconnectHandler);
      clientWrapper.on('error', errorHandler);
      clientWrapper.on('message', messageHandler);

      // Verify listeners were registered (they exist on the EventEmitter)
      expect(clientWrapper.listenerCount('connected')).toBe(1);
      expect(clientWrapper.listenerCount('disconnected')).toBe(1);
      expect(clientWrapper.listenerCount('error')).toBe(1);
      expect(clientWrapper.listenerCount('message')).toBe(1);
    });

    test('should support multiple listeners for same event', () => {
      const handler1 = mock(() => {});
      const handler2 = mock(() => {});

      clientWrapper.on('connected', handler1);
      clientWrapper.on('connected', handler2);

      expect(clientWrapper.listenerCount('connected')).toBe(2);
    });
  });
});