import { describe, test, expect, beforeEach, afterEach, mock } from 'bun:test';
import { Binding, BindingState } from '../src/binding/binding';
import { BindingManager, ManagerState } from '../src/binding/manager';
import type { BindingConfig } from '../src/types';

describe('Integration Tests', () => {
  describe('Binding Integration', () => {
    let binding: Binding;

    const mockConfig: BindingConfig = {
      name: 'test-integration-binding',
      source: {
        brokerUrl: 'mqtt://localhost:1883',
        username: 'mqtt',
        password: 'mqtt007',
        topic: 'test/source/+'
      },
      target: {
        brokerUrl: 'mqtt://localhost:1883',
        username: 'mqtt',
        password: 'mqtt007',
        topic: 'test/target/${topic}/forwarded'
      }
    };

    beforeEach(() => {
      binding = new Binding(mockConfig);
    });

    afterEach(async () => {
      if (binding && binding.isRunning) {
        await binding.stop();
      }
    });

    test('should create binding with correct configuration', () => {
      expect(binding.name).toBe('test-integration-binding');
      expect(binding.currentState).toBe(BindingState.STOPPED);
      expect(binding.isRunning).toBe(false);

      const config = binding.configuration;
      expect(config.name).toBe('test-integration-binding');
      expect(config.source.brokerUrl).toBe('mqtt://localhost:1883');
      expect(config.target.topic).toBe('test/target/${topic}/forwarded');
    });

    test('should handle binding lifecycle events', async () => {
      const stateChanges: BindingState[] = [];
      const events: string[] = [];

      binding.on('state-changed', (oldState, newState) => {
        stateChanges.push(newState);
      });

      binding.on('source-connected', () => events.push('source-connected'));
      binding.on('target-connected', () => events.push('target-connected'));
      binding.on('source-disconnected', () => events.push('source-disconnected'));
      binding.on('target-disconnected', () => events.push('target-disconnected'));
      binding.on('error', (error) => events.push(`error: ${error.message}`));

      // Test state transitions
      expect(binding.currentState).toBe(BindingState.STOPPED);

      // Note: These tests would normally require actual MQTT brokers
      // For unit testing, we're primarily testing the state management
      // and event emission patterns
      expect(stateChanges).toEqual([]);
      expect(events).toEqual([]);
    });

    test('should emit events for message forwarding', () => {
      const forwardedMessages: Array<{
        sourceTopic: string;
        targetTopic: string;
        qos: number;
      }> = [];

      binding.on('message-forwarded', (sourceTopic, targetTopic, qos) => {
        forwardedMessages.push({ sourceTopic, targetTopic, qos });
      });

      // In a real integration test, we would publish messages and verify forwarding
      // For now, we verify the event handler is properly set up
      expect(forwardedMessages).toEqual([]);
    });

    test('should handle topic transformation in message flow', () => {
      // Test that the binding uses the transformation logic correctly
      const config = binding.configuration;
      expect(config.target.topic).toBe('test/target/${topic}/forwarded');

      // The actual transformation would be tested when messages flow through
      // This verifies the configuration is preserved correctly
    });

    test('should generate unique client IDs for source and target', () => {
      // Access private clients for testing
      const sourceClient = (binding as any).sourceClient;
      const targetClient = (binding as any).targetClient;

      const sourceClientId = (sourceClient as any).clientId;
      const targetClientId = (targetClient as any).clientId;

      expect(sourceClientId).toBeDefined();
      expect(targetClientId).toBeDefined();
      expect(sourceClientId).not.toBe(targetClientId);

      expect(sourceClientId).toContain('test-integration-binding-source');
      expect(targetClientId).toContain('test-integration-binding-target');
    });
  });

  describe('Binding Manager Integration', () => {
    let manager: BindingManager;

    const createTestBinding = (name: string): BindingConfig => ({
      name,
      source: {
        brokerUrl: 'mqtt://localhost:1883',
        username: 'mqtt',
        password: 'mqtt007',
        topic: `test/${name}/input`
      },
      target: {
        brokerUrl: 'mqtt://localhost:1883',
        username: 'mqtt',
        password: 'mqtt007',
        topic: `test/${name}/output/\${topic}`
      }
    });

    beforeEach(() => {
      manager = new BindingManager();
    });

    afterEach(async () => {
      if (manager && manager.isRunning) {
        await manager.stopAll();
      }
    });

    test('should manage multiple bindings independently', () => {
      const binding1 = createTestBinding('binding1');
      const binding2 = createTestBinding('binding2');
      const binding3 = createTestBinding('binding3');

      manager.addBinding(binding1);
      manager.addBinding(binding2);
      manager.addBinding(binding3);

      expect(manager.bindingCount).toBe(3);
      expect(manager.bindingNames).toEqual(['binding1', 'binding2', 'binding3']);
      expect(manager.currentState).toBe(ManagerState.STOPPED);
    });

    test('should track binding statistics', () => {
      const binding1 = createTestBinding('stats-test-1');
      const binding2 = createTestBinding('stats-test-2');

      manager.addBinding(binding1);
      manager.addBinding(binding2);

      const stats = manager.getStats();
      expect(stats).toHaveLength(2);

      const stat1 = stats.find(s => s.bindingName === 'stats-test-1');
      const stat2 = stats.find(s => s.bindingName === 'stats-test-2');

      expect(stat1).toBeDefined();
      expect(stat2).toBeDefined();
      expect(stat1?.messagesForwarded).toBe(0);
      expect(stat1?.errorCount).toBe(0);
      expect(stat1?.state).toBe(BindingState.STOPPED);
    });

    test('should handle binding events and update statistics', () => {
      const binding = createTestBinding('event-test');
      manager.addBinding(binding);

      const managerEvents: string[] = [];

      manager.on('binding-started', (name) => managerEvents.push(`started:${name}`));
      manager.on('binding-stopped', (name) => managerEvents.push(`stopped:${name}`));
      manager.on('binding-error', (name, error) => managerEvents.push(`error:${name}:${error.message}`));
      manager.on('message-forwarded', (name, source, target, qos) =>
        managerEvents.push(`forwarded:${name}:${source}->${target}:${qos}`)
      );

      // Events will be emitted when actual binding operations occur
      expect(managerEvents).toEqual([]);
    });

    test('should prevent modifications while running', () => {
      const binding = createTestBinding('modification-test');
      manager.addBinding(binding);

      // Simulate running state
      (manager as any).setState(ManagerState.RUNNING);

      expect(() => {
        manager.addBinding(createTestBinding('should-fail'));
      }).toThrow('Cannot add bindings while manager is running');

      expect(async () => {
        await manager.removeBinding('modification-test');
      }).toThrow('Cannot remove bindings while manager is running');
    });

    test('should validate binding name uniqueness', () => {
      const binding1 = createTestBinding('duplicate-name');
      const binding2 = createTestBinding('duplicate-name');

      manager.addBinding(binding1);

      expect(() => {
        manager.addBinding(binding2);
      }).toThrow("Binding with name 'duplicate-name' already exists");
    });

    test('should handle manager state transitions', () => {
      const stateChanges: ManagerState[] = [];

      manager.on('state-changed', (oldState, newState) => {
        stateChanges.push(newState);
      });

      expect(manager.currentState).toBe(ManagerState.STOPPED);

      // Simulate state transitions
      (manager as any).setState(ManagerState.STARTING);
      (manager as any).setState(ManagerState.RUNNING);
      (manager as any).setState(ManagerState.STOPPING);
      (manager as any).setState(ManagerState.STOPPED);

      expect(stateChanges).toEqual([
        ManagerState.STARTING,
        ManagerState.RUNNING,
        ManagerState.STOPPING,
        ManagerState.STOPPED
      ]);
    });

    test('should provide individual binding status', () => {
      const binding1 = createTestBinding('status-test-1');
      const binding2 = createTestBinding('status-test-2');

      manager.addBinding(binding1);
      manager.addBinding(binding2);

      expect(manager.getBindingState('status-test-1')).toBe(BindingState.STOPPED);
      expect(manager.getBindingState('status-test-2')).toBe(BindingState.STOPPED);
      expect(manager.getBindingState('non-existent')).toBeUndefined();

      expect(manager.isBindingRunning('status-test-1')).toBe(false);
      expect(manager.isBindingRunning('status-test-2')).toBe(false);
      expect(manager.isBindingRunning('non-existent')).toBe(false);
    });

    test('should handle empty manager operations', () => {
      expect(manager.bindingCount).toBe(0);
      expect(manager.bindingNames).toEqual([]);
      expect(manager.getStats()).toEqual([]);
      expect(manager.currentState).toBe(ManagerState.STOPPED);
      expect(manager.isRunning).toBe(false);
    });
  });

  describe('End-to-End Configuration Integration', () => {
    test('should handle complete application configuration', () => {
      const appConfig = {
        logLevel: 'debug' as const,
        bindings: [
          {
            name: 'sensor-data-forwarder',
            source: {
              brokerUrl: 'mqtt://iot-sensors:1883',
              username: 'sensor_reader',
              password: 'sensor_pass',
              topic: 'sensors/+/data'
            },
            target: {
              brokerUrl: 'mqtt://data-warehouse:1883',
              username: 'warehouse_writer',
              password: 'warehouse_pass',
              topic: 'warehouse/${topic}/processed'
            }
          },
          {
            name: 'alert-forwarder',
            source: {
              brokerUrl: 'mqtt://alert-system:1883',
              topic: 'alerts/#'
            },
            target: {
              brokerUrl: 'mqtt://notification-service:1883',
              topic: 'notifications/alerts/${topic}'
            }
          }
        ]
      };

      const manager = new BindingManager();

      // Add all bindings from configuration
      for (const bindingConfig of appConfig.bindings) {
        manager.addBinding(bindingConfig);
      }

      expect(manager.bindingCount).toBe(2);

      const names = manager.bindingNames;
      expect(names).toContain('sensor-data-forwarder');
      expect(names).toContain('alert-forwarder');

      // Verify configuration preservation
      const sensorStats = manager.getBindingStats('sensor-data-forwarder');
      const alertStats = manager.getBindingStats('alert-forwarder');

      expect(sensorStats).toBeDefined();
      expect(alertStats).toBeDefined();
      expect(sensorStats?.bindingName).toBe('sensor-data-forwarder');
      expect(alertStats?.bindingName).toBe('alert-forwarder');
    });

    test('should handle real-world topic transformation patterns', () => {
      // Test patterns from the PRD
      const testCases = [
        {
          name: 'no-transformation',
          source: { brokerUrl: 'mqtt://test:1883', topic: 'a/b/c' },
          target: { brokerUrl: 'mqtt://test:1883' }, // No topic = no transformation
        },
        {
          name: 'append-transformation',
          source: { brokerUrl: 'mqtt://test:1883', topic: 'a/b/c' },
          target: { brokerUrl: 'mqtt://test:1883', topic: 'x/y/${topic}' },
        },
        {
          name: 'wrap-transformation',
          source: { brokerUrl: 'mqtt://test:1883', topic: 'a/b/c' },
          target: { brokerUrl: 'mqtt://test:1883', topic: 'x/y/${topic}/z' },
        },
        {
          name: 'static-transformation',
          source: { brokerUrl: 'mqtt://test:1883', topic: 'a/b/c' },
          target: { brokerUrl: 'mqtt://test:1883', topic: 'x/y/z' },
        }
      ];

      const manager = new BindingManager();

      for (const testCase of testCases) {
        const binding: BindingConfig = {
          name: testCase.name,
          source: testCase.source,
          target: testCase.target
        };

        manager.addBinding(binding);
      }

      expect(manager.bindingCount).toBe(4);
      expect(manager.bindingNames).toEqual([
        'no-transformation',
        'append-transformation',
        'wrap-transformation',
        'static-transformation'
      ]);
    });
  });
});