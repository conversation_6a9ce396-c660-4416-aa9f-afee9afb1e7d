import { describe, test, expect } from 'bun:test';
import { validateConfig } from '../src/config/validation';
import type { AppConfig } from '../src/types';

describe('Configuration Validation', () => {
  describe('validateConfig', () => {
    test('should validate a complete valid configuration', () => {
      const validConfig = {
        logLevel: 'info' as const,
        bindings: [
          {
            name: 'test-binding',
            source: {
              brokerUrl: 'mqtt://source-broker:1883',
              username: 'user',
              password: 'pass',
              topic: 'source/topic/+'
            },
            target: {
              brokerUrl: 'mqtt://target-broker:1883',
              username: 'user',
              password: 'pass',
              topic: 'target/${topic}/forwarded'
            }
          }
        ]
      };

      const result = validateConfig(validConfig);
      expect(result.logLevel).toBe('info');
      expect(result.bindings).toHaveLength(1);
      expect(result.bindings[0]!.name).toBe('test-binding');
    });

    test('should set default log level when not provided', () => {
      const configWithoutLogLevel = {
        bindings: [
          {
            source: {
              brokerUrl: 'mqtt://source:1883',
              topic: 'test/topic'
            },
            target: {
              brokerUrl: 'mqtt://target:1883'
            }
          }
        ]
      };

      const result = validateConfig(configWithoutLogLevel);
      expect(result.logLevel).toBe('info'); // Default value
    });

    test('should accept valid log levels', () => {
      const logLevels: Array<'debug' | 'info' | 'warn' | 'error'> = ['debug', 'info', 'warn', 'error'];

      for (const logLevel of logLevels) {
        const config = {
          logLevel,
          bindings: [
            {
              source: {
                brokerUrl: 'mqtt://test:1883',
                topic: 'test/topic'
              },
              target: {
                brokerUrl: 'mqtt://test:1883'
              }
            }
          ]
        };

        const result = validateConfig(config);
        expect(result.logLevel).toBe(logLevel);
      }
    });

    test('should validate minimal binding configuration', () => {
      const minimalConfig = {
        bindings: [
          {
            source: {
              brokerUrl: 'mqtt://source:1883',
              topic: 'test/topic'
            },
            target: {
              brokerUrl: 'mqtt://target:1883'
            }
          }
        ]
      };

      const result = validateConfig(minimalConfig);
      expect(result.bindings).toHaveLength(1);
      expect(result.bindings[0].source.brokerUrl).toBe('mqtt://source:1883');
      expect(result.bindings[0].target.brokerUrl).toBe('mqtt://target:1883');
    });

    test('should validate multiple bindings', () => {
      const multiBindingConfig = {
        bindings: [
          {
            name: 'binding-1',
            source: {
              brokerUrl: 'mqtt://source1:1883',
              topic: 'topic1'
            },
            target: {
              brokerUrl: 'mqtt://target1:1883'
            }
          },
          {
            name: 'binding-2',
            source: {
              brokerUrl: 'mqtt://source2:1883',
              topic: 'topic2'
            },
            target: {
              brokerUrl: 'mqtt://target2:1883',
              topic: 'transformed/${topic}'
            }
          }
        ]
      };

      const result = validateConfig(multiBindingConfig);
      expect(result.bindings).toHaveLength(2);
      expect(result.bindings[0].name).toBe('binding-1');
      expect(result.bindings[1].name).toBe('binding-2');
      expect(result.bindings[1].target.topic).toBe('transformed/${topic}');
    });

    test('should handle binding without name', () => {
      const configWithoutName = {
        bindings: [
          {
            source: {
              brokerUrl: 'mqtt://source:1883',
              topic: 'test/topic'
            },
            target: {
              brokerUrl: 'mqtt://target:1883'
            }
          }
        ]
      };

      const result = validateConfig(configWithoutName);
      expect(result.bindings[0].name).toBeUndefined();
    });

    test('should validate configuration with authentication', () => {
      const configWithAuth = {
        bindings: [
          {
            name: 'authenticated-binding',
            source: {
              brokerUrl: 'mqtt://secure-source:8883',
              username: 'source_user',
              password: 'source_pass',
              topic: 'secure/topic'
            },
            target: {
              brokerUrl: 'mqtt://secure-target:8883',
              username: 'target_user',
              password: 'target_pass',
              topic: 'secure/${topic}/output'
            }
          }
        ]
      };

      const result = validateConfig(configWithAuth);
      expect(result.bindings[0].source.username).toBe('source_user');
      expect(result.bindings[0].source.password).toBe('source_pass');
      expect(result.bindings[0].target.username).toBe('target_user');
      expect(result.bindings[0].target.password).toBe('target_pass');
    });

    test('should validate various broker URL formats', () => {
      const urlFormats = [
        'mqtt://localhost:1883',
        'mqtts://secure.broker.com:8883',
        'ws://websocket.broker.com:8080',
        'wss://secure-websocket.broker.com:8443',
        'tcp://tcp.broker.com:1883'
      ];

      for (const brokerUrl of urlFormats) {
        const config = {
          bindings: [
            {
              source: {
                brokerUrl,
                topic: 'test/topic'
              },
              target: {
                brokerUrl
              }
            }
          ]
        };

        const result = validateConfig(config);
        expect(result.bindings[0].source.brokerUrl).toBe(brokerUrl);
        expect(result.bindings[0].target.brokerUrl).toBe(brokerUrl);
      }
    });

    test('should validate wildcard topics', () => {
      const wildcardTopics = [
        'sensor/+/temperature',
        'device/+/+/status',
        'building/#',
        'data/+/sensor/#',
        'simple/topic'
      ];

      for (const topic of wildcardTopics) {
        const config = {
          bindings: [
            {
              source: {
                brokerUrl: 'mqtt://test:1883',
                topic
              },
              target: {
                brokerUrl: 'mqtt://test:1883'
              }
            }
          ]
        };

        const result = validateConfig(config);
        expect(result.bindings[0].source.topic).toBe(topic);
      }
    });

    test('should handle target without topic (no transformation)', () => {
      const config = {
        bindings: [
          {
            source: {
              brokerUrl: 'mqtt://source:1883',
              topic: 'source/topic'
            },
            target: {
              brokerUrl: 'mqtt://target:1883'
              // No topic specified - should use source topic as-is
            }
          }
        ]
      };

      const result = validateConfig(config);
      expect(result.bindings[0].target.topic).toBeUndefined();
    });

    test('should reject empty bindings array', () => {
      const emptyConfig = {
        bindings: []
      };

      expect(() => validateConfig(emptyConfig)).toThrow('bindings array cannot be empty');
    });

    test('should preserve all configuration properties', () => {
      const fullConfig = {
        logLevel: 'debug' as const,
        bindings: [
          {
            name: 'comprehensive-test',
            source: {
              brokerUrl: 'mqtt://source.example.com:1883',
              username: 'src_user',
              password: 'src_pass',
              topic: 'sensors/+/data'
            },
            target: {
              brokerUrl: 'mqtts://target.example.com:8883',
              username: 'tgt_user',
              password: 'tgt_pass',
              topic: 'archive/${topic}/processed'
            }
          }
        ]
      };

      const result = validateConfig(fullConfig);

      // Verify all properties are preserved
      expect(result.logLevel).toBe('debug');
      expect(result.bindings[0].name).toBe('comprehensive-test');
      expect(result.bindings[0].source.brokerUrl).toBe('mqtt://source.example.com:1883');
      expect(result.bindings[0].source.username).toBe('src_user');
      expect(result.bindings[0].source.password).toBe('src_pass');
      expect(result.bindings[0].source.topic).toBe('sensors/+/data');
      expect(result.bindings[0].target.brokerUrl).toBe('mqtts://target.example.com:8883');
      expect(result.bindings[0].target.username).toBe('tgt_user');
      expect(result.bindings[0].target.password).toBe('tgt_pass');
      expect(result.bindings[0].target.topic).toBe('archive/${topic}/processed');
    });
  });
});