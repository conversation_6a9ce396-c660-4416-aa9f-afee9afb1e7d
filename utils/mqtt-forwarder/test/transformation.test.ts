import { describe, test, expect } from 'bun:test';
import { transformTopic, validateTopicTemplate } from '../src/binding/transformation';

describe('Topic Transformation', () => {
  describe('transformTopic', () => {
    test('should return source topic when no template is provided', () => {
      const sourceTopic = 'a/b/c';
      const result = transformTopic(sourceTopic);
      expect(result).toBe('a/b/c');
    });

    test('should return source topic when template is undefined', () => {
      const sourceTopic = 'test/topic';
      const result = transformTopic(sourceTopic, undefined);
      expect(result).toBe('test/topic');
    });

    test('should return source topic when template is empty string', () => {
      const sourceTopic = 'sensor/data';
      const result = transformTopic(sourceTopic, '');
      expect(result).toBe('sensor/data');
    });

    test('should replace ${topic} placeholder with source topic', () => {
      const sourceTopic = 'a/b/c';
      const template = 'x/y/${topic}';
      const result = transformTopic(sourceTopic, template);
      expect(result).toBe('x/y/a/b/c');
    });

    test('should handle ${topic} placeholder at the beginning', () => {
      const sourceTopic = 'sensor/temp';
      const template = '${topic}/processed';
      const result = transformTopic(sourceTopic, template);
      expect(result).toBe('sensor/temp/processed');
    });

    test('should handle ${topic} placeholder in the middle', () => {
      const sourceTopic = 'device/123';
      const template = 'prefix/${topic}/suffix';
      const result = transformTopic(sourceTopic, template);
      expect(result).toBe('prefix/device/123/suffix');
    });

    test('should handle ${topic} placeholder at the end', () => {
      const sourceTopic = 'a/b/c';
      const template = 'x/y/${topic}';
      const result = transformTopic(sourceTopic, template);
      expect(result).toBe('x/y/a/b/c');
    });

    test('should handle multiple ${topic} placeholders', () => {
      const sourceTopic = 'test';
      const template = '${topic}/middle/${topic}';
      const result = transformTopic(sourceTopic, template);
      expect(result).toBe('test/middle/test');
    });

    test('should return template unchanged when no ${topic} placeholder is present', () => {
      const sourceTopic = 'a/b/c';
      const template = 'x/y/z';
      const result = transformTopic(sourceTopic, template);
      expect(result).toBe('x/y/z');
    });

    test('should handle complex topic structures', () => {
      const sourceTopic = 'building/floor1/room202/sensor/temperature';
      const template = 'archive/${topic}/data';
      const result = transformTopic(sourceTopic, template);
      expect(result).toBe('archive/building/floor1/room202/sensor/temperature/data');
    });

    test('should handle topics with special characters', () => {
      const sourceTopic = 'device-123/sensor_01';
      const template = 'processed/${topic}/output';
      const result = transformTopic(sourceTopic, template);
      expect(result).toBe('processed/device-123/sensor_01/output');
    });

    test('should handle empty source topic', () => {
      const sourceTopic = '';
      const template = 'prefix/${topic}/suffix';
      const result = transformTopic(sourceTopic, template);
      expect(result).toBe('prefix//suffix');
    });

    test('should handle single level topics', () => {
      const sourceTopic = 'temperature';
      const template = 'sensors/${topic}/data';
      const result = transformTopic(sourceTopic, template);
      expect(result).toBe('sensors/temperature/data');
    });

    test('should handle topics with numbers', () => {
      const sourceTopic = 'sensor/123/data';
      const template = 'archive/2024/${topic}';
      const result = transformTopic(sourceTopic, template);
      expect(result).toBe('archive/2024/sensor/123/data');
    });

    // Test cases from PRD requirements table
    test('PRD test case: a/b/c with no template', () => {
      const result = transformTopic('a/b/c');
      expect(result).toBe('a/b/c');
    });

    test('PRD test case: a/b/c with x/y/${topic}', () => {
      const result = transformTopic('a/b/c', 'x/y/${topic}');
      expect(result).toBe('x/y/a/b/c');
    });

    test('PRD test case: a/b/c with x/y/${topic}/z', () => {
      const result = transformTopic('a/b/c', 'x/y/${topic}/z');
      expect(result).toBe('x/y/a/b/c/z');
    });

    test('PRD test case: a/b/c with x/y/z (no placeholder)', () => {
      const result = transformTopic('a/b/c', 'x/y/z');
      expect(result).toBe('x/y/z');
    });
  });

  describe('validateTopicTemplate', () => {
    test('should return true for empty template', () => {
      expect(validateTopicTemplate('')).toBe(true);
    });

    test('should return true for undefined template', () => {
      expect(validateTopicTemplate(undefined as any)).toBe(true);
    });

    test('should return true for valid template with ${topic}', () => {
      expect(validateTopicTemplate('prefix/${topic}/suffix')).toBe(true);
    });

    test('should return true for template without placeholders', () => {
      expect(validateTopicTemplate('simple/topic/path')).toBe(true);
    });

    test('should return true for template with multiple ${topic} placeholders', () => {
      expect(validateTopicTemplate('${topic}/middle/${topic}')).toBe(true);
    });

    test('should return false for template with null characters', () => {
      expect(validateTopicTemplate('topic\x00/path')).toBe(false);
    });

    test('should return false for template with invalid placeholders', () => {
      expect(validateTopicTemplate('prefix/${invalid}/suffix')).toBe(false);
    });

    test('should return false for template with malformed placeholders', () => {
      expect(validateTopicTemplate('prefix/${topic/suffix')).toBe(false);
    });

    test('should return false for template with incomplete placeholders', () => {
      expect(validateTopicTemplate('prefix/$topic}/suffix')).toBe(false);
    });

    test('should return true for template with only ${topic}', () => {
      expect(validateTopicTemplate('${topic}')).toBe(true);
    });

    test('should return false for template with mixed valid and invalid placeholders', () => {
      expect(validateTopicTemplate('${topic}/${invalid}/${topic}')).toBe(false);
    });

    test('should handle templates with special MQTT topic characters', () => {
      expect(validateTopicTemplate('device-123/${topic}/sensor_01')).toBe(true);
    });

    test('should handle templates with numbers', () => {
      expect(validateTopicTemplate('archive/2024/${topic}/data')).toBe(true);
    });

    test('should return false for template with nested placeholders', () => {
      expect(validateTopicTemplate('${${topic}}')).toBe(false);
    });
  });
});