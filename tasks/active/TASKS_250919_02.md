# Task list info:

- name: 250919_02
- base_branch: develop

---

# Task list context

current_irrigation_plan_state and current_reservoir_state are very similar, as they represent a cycle. They have start and end times, which can be seen as a single record that starts with with no end time and at some point in the future gets an end time. The next lic packets which arrive with the same start time should be ignored if the end time already set (signing that the cycle is already completed). When start time changes, it's a new cycle and the record should be "reset".
That's the reason why we have triggers that sets the id primary key to the same value as the irrigation_plan (packages/directus/migrations/20250903G-irrigation-plan-state-set-id-trigger.js) or reservoir (packages/directus/migrations/20250904C-reservoir-state-set-id-trigger.js), so we always have a single record per irrigation plan or reservoir, indicating the current state (or cycle).
The history tables, irrigation_plan_state and reservoir_state, should not be append-only, but they should store a record for every "cycle" (start_time), even if the cycle is not completed (end_time is null). When the cycle is completed, the record should be updated with the end_time in the history table.
So, it makes sense to have the hyper tables primary keys of these tables as (irrigation_plan, start_time) and (reservoir, start_time) respectively.

THe plan is to:

- Change irrigation_plan_state, reservoir_state hyper tables PKs and time columns to use start_time instead of packet_date. Requirements: keep the tables canonical names (will involve renaming the tables as well), load all existing data into the new tables.
- Stick with the "one row per cycle" behavior for current_irrigation_plan_state and current_reservoir_state.
- Populate history tables from current tables through triggers on INSERT and UPDATE, keeping the "one row per cycle" behavior.

- Objective: migrate irrigation/reservoir state history to one-row-per-cycle semantics while keeping canonical table names.
- Constraints: rebuild hypertables with `(entity, start_time)` PK/time columns; backfill data; preserve triggers that project current state to history.
- Plan overview:
  1. Stage new hypertables (`*_new`), migrate data, swap names, and restore policies.
  2. Reinstate current-table triggers so inserts/updates maintain history rows per cycle.
  3. Update MQTT integration code/tests to align with the new schema and guards.
  4. Verify with migrations/tests before re-enabling writers.

**A note on "inferred end times":**

ISchedulingReportData and IAutomationReportData (both in packages/protobuf/dist/bundle.d.ts) have a status field. When status is 1, the irrigation plan (or the reservoir fill automation) execution is completed. They also have an end_time field. When end_time is 0, it means the end_time is missing.
There is an edge case where the plan (or automation) is completed, but the end_time is missing. In this case, we currently infer that the end_time is the packet_date. This can cause issues because on each packet we receive with status 1 and end_time 0, we infer end_time to be the packet_date and possible update the current_irrigation_plan_state (or current_reservoir_state) record with a new end_time.
We must change this so that the when updating the record, end_time can only be updated in two cases:

- When end_time was inferred and the record end_time is null (first time we are learning the end_time)
- When end_time is not missing (end_time != 0)

It makes sense too to save the information that the end_time was inferred in the database, so we can keep track of it and avoid updating the end_time if it was already set.

**Possible relevant files:**
Relevant files are:

- all migrations in packages/directus/migrations that have "irrigation-plan-state" or "reservoir-state" in the name.
- packages/mqtt-integration/src/db/mutations/irrigation-plan-state.ts (mutation functions)
- packages/mqtt-integration/src/db/mutations/reservoir-state.ts (mutation functions)
- packages/mqtt-integration/src/irriganet/irrigation-plan-state-calculator.ts (state calculation)
- packages/mqtt-integration/src/irriganet/reservoir-state-calculator.ts (state calculation)
- packages/mqtt-integration/src/irriganet/package-processors/default-processors/scheduling-report-package.ts (package processor)
- packages/mqtt-integration/src/irriganet/package-processors/default-processors/automation-report-package.ts (package processor)
- packages/protobuf/proto/scheduling_report.proto (protobuf definition)
- packages/protobuf/proto/automation_report.proto (protobuf definition)

---

# Tasks

## Task 1. Rebuild irrigation plan state history flow

**Description**
Refactor the irrigation plan state persistence to store exactly one history row per execution cycle. Replace the hypertables so they use `(entity, start_time)` as the primary key/time dimension, re-wire the current-state triggers to maintain history, introduce inferred end-time guards, and update application logic/tests to match.

**Acceptance Criteria**

- `irrigation_plan_state` hypertable keep their canonical names but use `start_time` as both hypertable time column and part of the primary key.
- All existing history data is migrated without loss, deduplicated so each `(entity, start_time)` pair is unique.
- `current_irrigation_plan_state` retain single-row-per-entity semantics, with triggers repopulating/updating history rows when cycles start or complete.
- MQTT integration code and automated tests cover inferred end-time behavior under the new schema.

**Status:** Done

### Subtask 1.1. Audit current schema and data flows

**Description**
Review existing irrigation state hypertables, triggers, and MQTT persistence paths to confirm migration scope and capture edge cases (including inferred end-time usage).

**Target directories**

- packages/directus/migrations (hypertable definitions)
- packages/mqtt-integration/src (state calculators and mutations)

**Assignee:** Codex
**Status:** Done

**Complexity:** Medium

**Dependencies:** None

**Estimated effort:** ~20 minutes

### Subtask 1.2. Implement new irrigation hypertable structure

**Description**
Create migration scripts that build replacement hypertables keyed by `(irrigation_plan_id, start_time)`, swap them into canonical names, and recreate required indexes/policies.

**Target directories**

- packages/directus/migrations (new hypertable migration scripts)

**Assignee:** Codex
**Status:** Done

**Complexity:** Medium

**Dependencies:** Subtask 1.1

**Estimated effort:** ~25 minutes

### Subtask 1.3. Backfill and deduplicate irrigation history data

**Description**
Populate the new hypertables from legacy data while enforcing one row per `(irrigation_plan_id, start_time)` and preserving inferred end-time flags.

**Target directories**

- packages/directus/migrations (data backfill logic)

**Assignee:** Codex
**Status:** Done

**Complexity:** Medium

**Dependencies:** Subtask 1.2

**Estimated effort:** ~20 minutes

### Subtask 1.4. Rewire irrigation triggers and guards

**Description**
Update current-state triggers and related DB/TypeScript logic so inferred end times are only applied once and history rows sync correctly per cycle.

**Target directories**

- packages/directus/migrations (trigger updates)
- packages/mqtt-integration/src/db/mutations (mutation guards)

**Assignee:** Codex
**Status:** Done

**Complexity:** Medium

**Dependencies:** Subtask 1.3

**Estimated effort:** ~20 minutes

### Subtask 1.5. Update irrigation integration tests and docs

**Description**
Adjust MQTT irrigation code paths, add regression tests for inferred end-time behavior, and update relevant documentation to match the new schema.

**Target directories**

- packages/mqtt-integration/src (code and tests)
- docs/guidelines/backend (schema change notes)

**Assignee:** Codex
**Status:** Done

**Complexity:** Medium

**Dependencies:** Subtask 1.4

**Estimated effort:** ~20 minutes

### Subtask 1.6. Document irrigation DDL and entity updates

**Description**
Record the irrigation history schema changes and inferred end-time semantics in the DDL references and entity overviews.

**Target directories**

- docs/guidelines/backend/ddl-changes.md (DDL documentation update guidelines)
- docs/001-ENTITIES.md (entity definitions)

**Complexity:** Low

**Dependencies:** Subtask 1.5

**Estimated effort:** ~15 minutes

**Assignee:** Codex
**Status:** Done

## Task 2. Rebuild reservoir state history flow

**Description**
Refactor the reservoir state persistence to store exactly one history row per execution cycle. Replace the hypertables so they use `(entity, start_time)` as the primary key/time dimension, re-wire the current-state triggers to maintain history, introduce inferred end-time guards, and update application logic/tests to match.

**Acceptance Criteria**

- `reservoir_state` hypertable keep their canonical names but use `start_time` as both hypertable time column and part of the primary key.
- All existing history data is migrated without loss, deduplicated so each `(entity, start_time)` pair is unique.
- `current_reservoir_state` retain single-row-per-entity semantics, with triggers repopulating/updating history rows when cycles start or complete.
- MQTT integration code and automated tests cover inferred end-time behavior under the new schema.

**Status:** Done

### Subtask 2.1. Review reservoir schema and data flows

**Description**
Inspect the current reservoir hypertable, triggers, and MQTT pathways to confirm migration requirements and reservoir-specific edge cases.

**Target directories**

- packages/directus/migrations (reservoir schema references)
- packages/mqtt-integration/src (reservoir calculators and mutations)

**Assignee:** Codex
**Status:** Done
**Complexity:** Medium

**Dependencies:** None

**Estimated effort:** ~20 minutes

### Subtask 2.2. Implement new reservoir hypertable structure

**Description**
Author migrations that introduce the `(reservoir_id, start_time)` hypertable, swap canonical names, and reinstate necessary indexes/policies.

**Target directories**

- packages/directus/migrations (new reservoir hypertable migration scripts)

**Assignee:** Codex
**Status:** Done

**Complexity:** Medium

**Dependencies:** Subtask 2.1

**Estimated effort:** ~25 minutes

### Subtask 2.3. Backfill and deduplicate reservoir history data

**Description**
Load legacy reservoir history into the new hypertable while eliminating duplicate `(reservoir_id, start_time)` entries and carrying inferred end-time metadata forward.

**Target directories**

- packages/directus/migrations (data migration logic)

**Assignee:** Codex
**Status:** Done
**Complexity:** Medium

**Dependencies:** Subtask 2.2

**Estimated effort:** ~20 minutes

### Subtask 2.4. Rewire reservoir triggers and guards

**Description**
Update reservoir current-state triggers and mutation logic to respect the single inferred end-time rule and maintain one-row-per-cycle synchronization.

**Target directories**

- packages/directus/migrations (trigger updates)
- packages/mqtt-integration/src/db/mutations (reservoir guard logic)

**Assignee:** Codex
**Status:** Done
**Complexity:** Medium

**Dependencies:** Subtask 2.3

**Estimated effort:** ~20 minutes

### Subtask 2.5. Update reservoir integration tests and docs

**Description**
Modify MQTT reservoir processors/tests and update documentation to reflect the rebuilt reservoir history flow and inferred end-time handling.

**Target directories**

- packages/mqtt-integration/src (code and tests)
- docs/guidelines/backend (documentation updates)

**Assignee:** Codex
**Status:** Done
**Complexity:** Medium

**Dependencies:** Subtask 2.4

**Estimated effort:** ~20 minutes

### Subtask 2.6. Document reservoir DDL and entity updates

**Description**
Capture the reservoir history schema revisions and inferred end-time handling within the DDL documentation and entity catalog.

**Target directories**

- docs/guidelines/backend/ddl-changes.md (DDL documentation update guidelines)
- docs/001-ENTITIES.md (entity definitions)

**Complexity:** Low

**Dependencies:** Subtask 2.5

**Estimated effort:** ~15 minutes

**Assignee:** Codex
**Status:** Done
