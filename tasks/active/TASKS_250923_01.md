# Task list info:

- name: 250923_01
- base_branch: develop

---

# Tasks

## Task 1. codec simulator periodic reports

**Description**
Note: LIC and CODEC are different names for the same device.
The real LIC device sends periodic reports to the MQTT broker. It sends status (packages/protobuf/proto/status.proto) messages every 1 minute (configurable).
To understand another peridic reports, check the file docs/PRODUCT_OVERVIEW.md.
The simulator must reproduce this behavior.
It has to:

- Send status messages every 1 minute (configurable).
- Send scheduling_report as described in docs/PRODUCT_OVERVIEW.md.
- Send automation_report as described in docs/PRODUCT_OVERVIEW.md.

**Analysis Results:**
The current codec simulator implementation does NOT include periodic reporting functionality. The following features are missing:

1. **Status messages**: No periodic SystemStatusPackage messages are being sent
2. **Scheduling reports**: The scheduling logic has a TODO comment but no actual SchedulingReportPackage implementation
3. **Automation reports**: No AutomationReportPackage messages are being sent

**Implementation Requirements:**

1. Add periodic status reporting (SystemStatusPackage) every 1 minute (configurable)
2. Implement SchedulingReportPackage sending when schedules start/complete
3. Implement AutomationReportPackage sending when automation events occur
4. Add configuration for report intervals

**Target directories**

- packages/codec-simulator (codec simulator)

**Status:** Completed

### Subtask 1.1. Implement periodic status reporting system

**Description**
Add configuration for report intervals and implement the core periodic status reporting infrastructure. This includes:
- Adding report interval configuration to SystemConfig
- Implementing interval timers for periodic reporting
- Creating the SystemStatusPackage construction infrastructure
- Adding MQTT sending methods for status reports

**Target directories**

- packages/codec-simulator/src/simulator.ts
- packages/codec-simulator/src/state/types.ts
- packages/codec-simulator/src/protocol/handler.ts

**Assignee:** Claude

**Status:** Done

### Subtask 1.2. Implement status report content generation

**Description**
Implement the actual SystemStatusPackage content generation with proper bitmask calculations. This includes:
- Calculating sync/on/input bitmasks from device states
- Handling rain-related fields (raining, rainfall, paused_time)
- Integrating with device state for accurate status reporting
- Adding proper timestamp handling for periodic reports

**Target directories**

- packages/codec-simulator/src/simulator.ts
- packages/codec-simulator/src/logic/device.ts
- packages/codec-simulator/src/state/types.ts

**Assignee:** Claude

**Status:** Done

### Subtask 1.3. Implement scheduling report functionality

**Description**
Add SchedulingReportPackage sending when irrigation schedules start and complete. This includes:
- Hooking into scheduling logic to detect schedule execution
- Tracking schedule execution state and timestamps
- Generating proper SchedulingReportData with sector/fertigation bitmasks
- Handling water pump status and backwash information
- Adding execution status codes for different completion states

**Target directories**

- packages/codec-simulator/src/logic/scheduling.ts
- packages/codec-simulator/src/simulator.ts

**Assignee:** Claude

**Status:** Done

### Subtask 1.4. Implement automation report functionality

**Description**
Add AutomationReportPackage sending when reservoir automation events occur. This includes:
- Hooking into automation logic to detect automation state changes
- Tracking automation execution with proper timestamps
- Generating AutomationReportData with start/restart/end times
- Adding execution status tracking for automation events
- Implementing periodic automation status reporting

**Target directories**

- packages/codec-simulator/src/logic/automation.ts
- packages/codec-simulator/src/simulator.ts

**Assignee:** Claude

**Status:** Done
