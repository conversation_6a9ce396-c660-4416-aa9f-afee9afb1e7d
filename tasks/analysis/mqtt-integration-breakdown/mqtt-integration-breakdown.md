# MQTT Integration Package Breakdown

## 1. Package Overview
The `packages/mqtt-integration` service is a Bun-based worker that bridges MQTT-connected IrrigaNet devices (LIC controllers and peripherals) with the platform data layer. It polls Directus-managed PostgreSQL tables for outgoing device commands, pushes them to devices over MQTT, ingests device telemetry encoded with the shared `proto` bundle, and persists derived state back to the database. The entrypoint (`src/index.ts`) composes dependencies through `src/container.ts`, wiring configuration, database access, Directus cache invalidation, MQTT transport, and the `CodecManagerRegistry` that keeps per-device state machines active.

## 2. Core Responsibilities
- Poll `device_message_request` records, determine processing order, and dispatch commands to the appropriate device transport (`DeviceMessageQueueService`).
- Maintain MQTT connectivity, handle topic routing, and marshal protobuf payloads to/from codec devices (`transport/mqtt`).
- Build codec-specific protobuf command packets and update request metadata before transmission (`CodecManager`).
- Parse device telemetry packets, compute higher-level domain states (projects, irrigation plans, reservoirs), and record them in Timescale/Directus tables (`irriganet/package-processors`).
- Load and hydrate LIC configuration/state from PostgreSQL, detect when configuration is stale, and schedule refreshes (`irriganet/db-loader`, `CodecManager.updateFlags`).
- Centralize runtime configuration, environment overrides, debugging toggles, and structured logging (`config.ts`, `log.ts`).

## 3. Component Analysis
### 3.1 Entry points & initialization logic
- `src/index.ts` bootstraps the application: initializes the `CodecManagerRegistry`, constructs `DeviceMessageQueueService`, wires queue events to codec handlers, schedules periodic LIC refreshes, and registers shutdown hooks that stop polling and close the MQTT client.
- `src/container.ts` acts as the composition root. It initializes `ConfigHolder`, instantiates the Directus client, database connection (`createDB`), MQTT client, transport factory, default packet processor, and the codec registry. Mutation queries trigger `DirectusClient.clearCache()` via a throttled callback, ensuring Directus reflects state changes.

### 3.2 Database connection & query handling
- `src/db/connection.ts` builds a `postgres` client with logging hooks (`LoggerManager`), optional mutation callback, and sane pooling defaults.
- Read models live under `src/db/queries/**`: device message queues, LIC trees, scheduling metadata, reservoirs, etc. Queries typically normalize joins into nested object graphs for the domain layer.
- Mutations in `src/db/mutations/**` persist telemetry snapshots, queue state transitions, and LIC state updates. They encapsulate conflict handling (e.g., `batchInsertIrrigationPlanStates`) and retry semantics.
- `DeviceMessageQueueService` (`src/db/services/device-message-queue.ts`) encapsulates polling cadence, retry selection, and event emission without mutating rows itself, keeping DB writes centralized in codec handlers.

### 3.3 MQTT client & transport layer
- `transport/mqtt/mqtt-client.ts` wraps the `mqtt` v5 client, applying Bun-friendly lifecycle hooks, read-only safeguards, and graceful shutdown.
- `transport/mqtt/mqtt-transport-factory.ts` subscribes to `/codec/+/report`, demultiplexes messages per LIC, and exposes factory-created transports that send downlink payloads to `/codec/{lic}/downlink` with publish acknowledgements.
- `transport/mqtt/handle-message.ts` normalizes raw MQTT topics into deviceId/topicType pairs used by upstream handlers.

### 3.4 Protocol buffer message processing
- `irriganet/codec-manager.ts` is the central orchestrator for a single LIC. It decodes incoming `codec.out.OutgoingPacket` payloads (falling back to JSON), queues packet persistence via `fastq`, delegates payload-specific handling to `IOutgoingPacketProcessor`, and manages the send lifecycle: building protobuf packets, updating DB status, and marking results.
- `irriganet/proto.ts` contains builders that translate LIC state data structures into the generated protobuf message shapes for config, scheduling, automation, and control messages, using `appendCRC16` to finalize payloads.
- `irriganet/package-processors/**` implements default telemetry handlers (`processInfoPackage`, `processStatusPackage`, etc.) that translate telemetry into domain mutations (e.g., irrigation plan state inserts, reservoir state upserts).

### 3.5 Device state management
- `irriganet/codec-manager-registry.ts` caches `CodecManager` instances per LIC identifier, eagerly preloads LICs from the database, and exposes `handleDeviceMessageRequest`/`handleDeviceMessageRetry` entrypoints used by the queue service. It also owns periodic `fetchLICS` refreshes and debug state dumps controlled via configuration.
- The registry builds codec state by invoking `loadLICStateByIdentifier` from `irriganet/db-loader`, which assembles property/LIC trees, generates mesh/device graphs, and produces schedule/sector structures using calculators in `irriganet/**-generator.ts` modules.
- Calculators such as `irrigation-plan-state-calculator.ts`, `project-state-calculator.ts`, and `reservoir-state-calculator.ts` interpret telemetry bitmasks and timelines into domain projections persisted by DB mutations.
- `CodecManager.updateFlags` tracks which configuration segments require refresh, coordinating message creation through `createDeviceMessageRequest` and updating LIC state timestamps to detect out-of-sync conditions.

### 3.6 Configuration management
- `config.ts` exposes structured configuration grouped by concern (MQTT, PostgreSQL, Directus, queue tuning, codec behavior, debug paths). `ConfigHolder` ensures a single instance with optional test overrides.
- Runtime consumers import `config` getters, ensuring lazy resolution and enabling `ConfigHolder.init()` replacement for tests.
- Debug controls (`debug.codecState`) gate file system writes of LIC state snapshots for inspection.

### 3.7 Logging & error handling
- `log.ts` provides a hierarchical logger with environment-controlled levels (`LOG_LEVEL`, `CHILDREN_LOG_LEVEL`). `LoggerManager` maintains logger instances and propagates overrides.
- Modules log at appropriate levels: queue lifecycle events, MQTT connection status, codec send/receive traces, DB notices, and error conditions (with fallback console assertions disabled by default).
- Error handling generally catches and logs exceptions around DB writes and packet processing to prevent the worker loop from crashing, while still surfacing failures in logs. Some processors rethrow (e.g., irrigation plan inserts) to signal upstream handling.

## 4. Architecture Assessment
### 4.1 Single Responsibility Principle (SRP)
- Well-scoped: `DeviceMessageQueueService` focuses solely on polling and event emission, leaving message handling elsewhere. Transport factory, logger, and configuration modules are similarly cohesive.
- SRP pressure points: `irriganet/codec-manager.ts` blends multiple responsibilities—state hydration, telemetry ingestion, protobuf building, queue coordination, file-system debug output, and retry orchestration—within a single class exceeding 700 lines. `codec-manager-registry.ts` mixes caching, DB refresh, event orchestration, and debug management. The DB loader package hosts both raw SQL orchestration and domain modeling, leading to dense modules.

### 4.2 Coupling Between Components
- Tight coupling exists between the codec manager layer and data access. `CodecManager` directly imports numerous mutations/queries, meaning DB schema changes ripple through the device orchestration layer.
- The transport factory assumes MQTT topic conventions and hands raw buffers to `CodecManager`, creating runtime coupling that complicates swapping transports.
- Directus cache invalidation is triggered inside the DB connection factory via mutation detection, coupling persistence concerns with an external service.
- Package processors invoke calculator modules and DB mutations directly, intertwining telemetry interpretation with persistence side-effects.

### 4.3 Areas of High Complexity or Mixed Concerns
- `codec-manager.ts` maintains intricate state machines (update flags, timers, packet queues) alongside I/O concerns, making the control flow difficult to reason about and test.
- `irriganet/db-loader` performs deep graph assembly from heterogeneous queries, and its public surface hides numerous implicit contracts (e.g., ord_idx mappings) spread across helper modules.
- Telemetry processors perform heavy domain logic inline with persistence, elevating cognitive load and limiting reuse.
- Composition root currently stitches together tightly coupled modules, making it challenging to substitute implementations (e.g., alternate transport or configuration source) without modifying internal modules.

## 5. Refactoring Proposal
### 5.1 Suggested Package Decomposition
1. **`@irriganet/runtime-mqtt`** – Bun entrypoint, lifecycle orchestration, DI container, and process-level concerns (signal handling, periodic tasks). Depends on the packages below via explicit interfaces.
2. **`@irriganet/device-message-queue`** – `DeviceMessageQueueService`, queue queries, and related types. Provides an event-driven interface for consumers; no knowledge of codec specifics.
3. **`@irriganet/data-access`** – Database connection factory, query/mutation repositories, and schema-driven types. Offers composable repositories used by higher layers without leaking transport or telemetry logic. Responsible for Directus cache hooks.
4. **`@irriganet/codec-transport-mqtt`** – MQTT client façade, topic parsing, and `ICodecTransportFactory` implementation. Exposes a minimal transport interface decoupled from codec state.
5. **`@irriganet/codec-state`** – Core codec domain: `CodecManager`, registry, update flag state machine, and integration with transport/queue abstractions. Depends on repositories from `data-access` and on an outgoing packet processor interface.
6. **`@irriganet/codec-telemetry`** – Packet processors, calculators, and state projection logic. Consumes transport/context models from `codec-state` and repositories from `data-access`; produces mutations/events without owning transport concerns.
7. **`@irriganet/config` & `@irriganet/logging` (optional)** – Shared configuration holder and logger reused by multiple services (aligns with future extraction hinted in comments).

### 5.2 Dependency Outline
- `runtime-mqtt` → `config`, `logging`, `codec-state`, `codec-transport-mqtt`, `device-message-queue`.
- `codec-state` ↔ `codec-telemetry` (processor interface) and depends on `data-access` repositories plus `codec-transport-mqtt` abstraction.
- `device-message-queue` → `data-access` (queries) and `logging`.
- `codec-telemetry` → `data-access`, `logging`, shared domain models from `codec-state`.
- `codec-transport-mqtt` → `config`, `logging`.
- `data-access` → Postgres client, `config`, `logging`, Directus API client.

### 5.3 Migration Strategy Considerations
1. **Stabilize Interfaces:** Define TypeScript interfaces for transport, repositories, and telemetry processors while the code still lives in the monorepo package. Add adapter layers so current implementation conforms to the future contract.
2. **Extract Shared Utilities:** Move `config.ts`, `log.ts`, and generic utilities into shared packages to eliminate circular dependencies when other packages are split.
3. **Carve Out Data Access:** Relocate `db/connection`, `db/queries`, and `db/mutations` into `@irriganet/data-access`, exporting typed repository functions. Update imports across the package to pull from the new module.
4. **Isolate Device Message Queue:** Move `DeviceMessageQueueService` and associated query helpers into `@irriganet/device-message-queue`, keeping its event contract intact. Adjust the runtime to consume it via the new package.
5. **Modularize Transport:** Extract the MQTT client/factory into `@irriganet/codec-transport-mqtt`, ensuring `CodecManagerRegistry` and the runtime consume only the `ICodecTransportFactory` interface.
6. **Separate Codec State & Telemetry:** Relocate `CodecManager`, registry, and db-loader to `@irriganet/codec-state`, refactoring direct DB imports to use repositories. Split packet processors and calculators into `@irriganet/codec-telemetry`, referenced through an injected processor registry.
7. **Incremental Testing:** After each extraction, run existing integration tests (`packages/mqtt-integration/tests/**`) and add focused unit tests for the new packages to verify behavior before removing old paths.

### 5.4 Benefits of the Proposed Separation
- **Improved testability:** Smaller packages with clearer boundaries make it straightforward to mock transports, repositories, or telemetry processors in isolation.
- **Easier evolvability:** Transport or database implementations can change without cascading modifications across the codec domain.
- **Operational flexibility:** Runtime orchestration becomes lighter, enabling alternate entrypoints (e.g., CLI triggers, scheduled jobs) to reuse queue or telemetry modules.
- **Shared reuse:** Configuration and logging packages can serve other workers (e.g., HTTP codec integration), ensuring consistent behavior and reducing duplication.
- **Risk mitigation:** Encapsulating persistence logic in `data-access` centralizes direct SQL knowledge, easing schema migrations and reducing the chance of redundant queries scattered across the codebase.

