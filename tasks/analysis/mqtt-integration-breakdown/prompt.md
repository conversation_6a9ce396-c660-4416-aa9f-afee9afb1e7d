Analyze the `packages/mqtt-integration` package and create comprehensive documentation covering:

1. **Package Overview**: Describe the main purpose and role within the broader system architecture
2. **Core Responsibilities**: List and explain each primary responsibility of the package
3. **Component Analysis**: Document all major components, their functions, and interdependencies including:

   - Entry points and initialization logic
   - Database connection and query handling
   - MQTT client and transport layer
   - Protocol buffer message processing
   - Device state management
   - Configuration management
   - Logging and error handling

4. **Architecture Assessment**: Evaluate the current architecture for:

   - Single Responsibility Principle adherence
   - Coupling between components
   - Areas of high complexity or mixed concerns

5. **Refactoring Proposal**: Propose a breakdown into separate packages following single responsibility principles, including:
   - Suggested package names and their specific responsibilities
   - Dependencies between the new packages
   - Migration strategy considerations
   - Benefits of the proposed separation

Focus on the TypeScript codebase structure, considering the Bun runtime environment and the integration with MQTT, PostgreSQL, Directus, and Protocol Buffers as shown in the codebase context.

Write the report in the new file file tasks/analysis/mqtt-integration-breakdown-gemini-cli.md
