# Task list info:

- name: 250919_01
- base_branch: develop

---

# Tasks

## Task 1. Change irrigation plan state end time handling in MQTT integration

**Description**
ISchedulingReportData has a status field. When status is 1, the plan execution is completed. It also has an end_time field. When end_time is 0, it means the end_time is missing.
There is an edge case where the plan is completed, but the end_time is missing. In this case, we currently infer that the end_time is the packet_date. This is causing issues because on each packet we receive with status 1 and end_time 0, we infer end_time to be the packet_date and update the irrigation_plan_state record with a new end_time.
We must change this so that the when updating the irrigation_plan_state record, end_time can only be updated in two cases:

- When end_time was inferred and the record end_time is null (first time we are learning the end_time)
- When end_time is not missing (end_time != 0)

To achieve the desired behavior we will:

- guard the information that end_time was inferred in the result of the irrigation plan state calculation.
- Need two irrigation_plan_state mutations: one that always updates the record end_time and one that only updates the record when the end_time if it is NULL (maybe a COALESCE in the upsert statement).
- When persisting the irrigation plan state, we will decide which mutation to use based on the guard information.

Relevant files are:

- packages/directus/migrations/20250903F-create-irrigation-plan-state-hypertable.js (table definition for reference)
- packages/mqtt-integration/src/db/mutations/irrigation-plan-state.ts (mutation functions)
- packages/mqtt-integration/src/irriganet/irrigation-plan-state-calculator.ts (state calculation)
- packages/mqtt-integration/src/irriganet/package-processors/default-processors/scheduling-report-package.ts (package processor)
- packages/protobuf/proto/scheduling_report.proto (protobuf definition)

**Target directories**

- packages/mqtt-integration (backend)

**Status:** Done

### Subtask 1.1. Capture end-time inference metadata

**Description**
Extend `calculateIrrigationPlanState` to surface whether `end_time` was inferred from `packet_date`, update the returned typing, and refresh existing tests to reflect the new shape.

**Target directories**

- packages/mqtt-integration/src/irriganet (backend)
- packages/mqtt-integration/tests (backend)

**Assignee:** Codex

**Status:** Done

### Subtask 1.2. Implement guarded irrigation plan state mutations

**Description**
Add mutation helpers so one path always writes `end_time` while another only fills it when the stored value is `NULL`, keeping trigger behaviour intact.

**Target directories**

- packages/mqtt-integration/src/db/mutations (backend)

**Assignee:** Codex

**Status:** Done

### Subtask 1.3. Route persistence through guarded mutations

**Description**
Update the scheduling report processor to choose between the new mutations based on the inferred flag while preserving batching and logging patterns.

**Target directories**

- packages/mqtt-integration/src/irriganet (backend)
- packages/mqtt-integration/src/db/mutations (backend)

**Assignee:** Codex

**Status:** Done

### Subtask 1.4. Expand irrigation plan state coverage

**Description**
Adjust calculator specs and add mutation-level tests to verify that inferred end times are only set once and real end times always win.

**Target directories**

- packages/mqtt-integration/tests (backend)

**Assignee:** Codex

**Status:** Done
