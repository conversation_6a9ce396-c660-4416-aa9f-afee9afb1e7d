# Task list info:

- name: 250918_02
- base_branch: develop

---

# Tasks

## Task 1. Irrigation plan overlap validation

**Description**
In the frontend app we must check for irrigation plan overlaps when creating or updating a plan.
Since a plan can take more than its estimated duration due to delays, like mesh communication errors, we must consider a configurable minimum delay of 30 minutes between the end of a plan and the start of another.
The configuration will be put in the app config (packages/app/src/utils/config.ts).
So the rule is: One plan must not start before 30 minutes after the estimated end of another plan.
Let's work with an example:

- Let's say we have a plan that starts at 06:00 and has 4 steps of 15 minutes each. The estimated end of the plan is 07:00. Another plan must not start before 07:30.
- Then, we create a second plan starting at 08:00. This is valid, as there is more than 30 minutes between the end of the first plan and the start of the second plan.
- Then we update the first plan to have 5 steps of 15 minutes each. The estimated end of the first plan is now 07:15. Since the time between the end of the first plan and the start of the second plan is 08:00 - 07:15 = 45 minutes, the update must be allowed.
- Then we update the first plan to have 6 steps of 15 minutes each. The estimated end of the first plan is now 07:30. Since the time between the end of the first plan and the start of the second plan is 08:00 - 07:30 = 30 minutes, the update must be allowed (the limit is inclusive).
- Then we update the first plan steps duration to 20 minutes each, keeping the same number of steps. The estimated end of the first plan is now 07:40. Since 08:00 - 07:40 = 20 minutes, which is less than the minimum delay, the update must not be allowed.
- Then we change the first plan to have 4 steps of 15 minutes each, but its start time is changed to 7:00. The estimated end of the first plan is now 08:00. Since the second plan starts at the same time, the update must not be allowed.

When creating a plan, we first create the irrigation plan record, and later we create the steps. So, at the first moment we don't know the estimated end of the plan. At this moment we can only check if there are any previous plan with estimated end within the minimum delay before the start of the new plan.
Only when we create the steps we know the estimated end of the plan, and we must check again for overlaps with following plans.

When checking for overlaps, it is important to take into account the days of the week. If we forget about it, it is possible to mistakenly disallow a plan that in fact will not overlap with another plan. For example, if we have a plan that starts at 06:00 and ends at 07:00 on Monday, and another plan that starts at 06:30 and ends at 07:30 on Tuesday, the validation must allow it.

There are 2 panels for configuring a plan:

- packages/app/src/pages/main/components/IrrigationPlanConfigPanel.tsx which contains the plan configuration, like name, days of week, start time, etc.
- packages/app/src/pages/main/components/IrrigationPlanStepsPanel2/index.tsx which contains the plan steps configuration.

IrrigationPlanStepsPanel2 already has some validations, and you can follow the same pattern to add the new validation.
IrrigationPlanConfigPanel does not have time validations yet, and you must add them.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 1.1. Add plan minimum gap configuration

**Description**
Expose a configurable `planMinimumGapMinutes` setting in the app config with a 30 minute default so UI validation can reference it.

**Target directories**

- packages/app/src/utils (frontend configuration)

**Status:** Done

### Subtask 1.2. Build irrigation plan overlap utility

**Description**
Create reusable helpers that calculate plan windows per weekday, apply the minimum gap rule, and return validation results for start-time and duration checks.

**Target directories**

- packages/app/src/pages/main/components (plan validation helpers)

**Status:** Done

### Subtask 1.3. Integrate overlap validation in config panel

**Description**
Wire the overlap utility into `IrrigationPlanPage` and `IrrigationPlanConfigPanel` so start time and day selection block conflicts with earlier plans and surface inline errors.

**Target directories**

- packages/app/src/pages/main (plan configuration flow)

**Status:** Done

### Subtask 1.4. Integrate overlap validation in steps panel

**Description**
Use the overlap utility inside `IrrigationPlanStepsPanel2` dialogs to revalidate plan end times after step edits and prevent conflicts with following plans.

**Target directories**

- packages/app/src/pages/main/components/IrrigationPlanStepsPanel2 (steps management UI)

**Status:** Done

## Task 2. Show estimated plan end time in IrrigationPlanCard

**Description**
Show the estimated plan end time in the IrrigationPlanCard component. The end time must be calculated based on the plan steps duration and the plan start time. The end time must be shown together with the start time, in this example format: "Amanhã de 04:30 às 06:00".

**Target directories**

- app (frontend)

**Status:** Done
